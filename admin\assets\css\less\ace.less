@import "bootstrap/variables.less";
@import "bootstrap/mixins.less";

@import "variables.less";//there are also some variables on top of some other less files
@import "mixins.less";


@import "general.less";//includes general basic styling of page 
@import "basic.less";//includes styling of some elements such as pagination, etc
@import "utility.less";//includes some utility classes such as headers, colors, font sizing, etc
@import "ace-nav.less";//ace top navigation

@import "breadcrumbs.less";
@import "searchbox.less";
@import "sidebar.less";


@import "buttons.less";
@import "label-badge.less";
@import "dropdown.less";
@import "form.less";
@import "tab-accordion.less";
@import "tables.less";
@import "widget.less";
@import "tooltip-popover.less";
@import "progressbar.less";
@import "infobox.less";

@import "page.pricing.less";
@import "page.login.less";
@import "page.invoice.less";
@import "page.error.less";
@import "gallery.less";
@import "items.less";
@import "page.profile.less";
@import "page.inbox.less";
@import "page.timeline.less";


@import "thirdparty-calendar.less";
@import "thirdparty-chosen.less";
@import "thirdparty-select2.less";
@import "thirdparty-colorbox.less";
@import "thirdparty-fuelux.less";//fuelux spinner, tree & wizard
@import "thirdparty-gritter.less";
@import "thirdparty-wysiwyg.less";
@import "thirdparty-editable.less";

@import "thirdparty-slider.less";//jquery ui slider
@import "thirdparty-jquery-ui.less";//other jquery ui widgets & elements
@import "thirdparty-jqgrid.less";//jqGrid plugin
@import "thirdparty-nestable.less";//nestable list
@import "thirdparty-dropzone.less";//dropzone.js


@import "icon-animated.less";

@import "other.less";//setting box, etc

@import "ext/bootstrap-tag.less";//less files provided by the thirdparty plugin, sometimes modified


@import "bs3-reset.less";//resetting box-sizing to default content-box for third party elements


@import "ace-responsive.less";