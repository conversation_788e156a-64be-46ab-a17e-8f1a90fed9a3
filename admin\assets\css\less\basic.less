//some elements variables
@blockquote-border:#E5EAF1;

@modal-footer-border:#E4E9EE;
@modal-footer-bg:#EFF3F8;

@pagination-color:#2283C5;
@pagination-border:#E0E8EB;
@pagination-bg:#FAFAFA;
@pagination-bg-hover:#EAEFF2;

    @pagination-bg-disabled:#F9F9F9;
@pagination-border-disabled:#D9D9D9;
 
 @pagination-color-active:#FFF;
    @pagination-bg-active:#6FAED9;
@pagination-border-active:#6FAED9;



/* elements */
[class*=" icon-"] , [class^="icon-"] {
	display:inline-block;
	text-align:center;
}

a{
 &:focus, &:active {
	text-decoration:none;
 }
}


/* header sizes */
.h-size(@index) when (@index > 0){
	@h-tag : ~`"h@{index}"`;
	@{h-tag} {
		@tmpvar : ~`"h@{index}-size"`;//get the variable h1-size, h2-size , etc...
		font-size:unit(@@tmpvar , px);
		font-weight:normal;
		font-family:"Open Sans","Helvetica Neue",Helvetica,Arial,sans-serif;
		
		&.smaller {
			font-size:unit((@@tmpvar - 1) , px);
		}
		&.bigger {
			font-size:unit((@@tmpvar + 1) , px);
		}
		&.block {
			margin-bottom:16px;
		}
	}
}
.h-size(1);
.h-size(2);
.h-size(3);
.h-size(4);
.h-size(5);
.h-size(6);




/* some list styling */
ul, ol {
 margin: 0 0 10px 25px;
 padding: 0;
 
 &.margin-5 { margin-left: 5px; }
 &.margin-10 { margin-left: 10px; }
 &.margin-15 { margin-left: 15px; }
 &.margin-20 { margin-left: 20px; }
}
li > ul,
li > ol {
 margin-left: 18px;
}

.list-unstyled , .list-inline {
 margin-left: 0;

 > li > [class*="icon-"]:first-child {
	width: 18px;
	text-align: center;
 }
}

.spaced > li {
 margin-top: 9px;
 margin-bottom: 9px;
}
.spaced2 > li {
 margin-top: 15px;
 margin-bottom: 15px;
}
li.divider {
 margin-top: 3px;
 margin-bottom: 3px;
 height: 0;
 font-size: 0;
 
 .spaced > & {
	margin-top: 5px;
	margin-bottom: 5px;
 }
 .spaced2 > & {
	margin-top: 8px;
	margin-bottom: 8px;
 }
 
 &:before {
	content: "";
	display: inline-block;
 }
}



/* little elements */
blockquote{
 &, &.pull-right {
	border-color: @blockquote-border;
 }
}


/* modals */
.modal-content {
 .border-radius(0);
 .box-shadow(none);
}
.modal-footer {
 padding-top: 12px;
 padding-bottom: 14px;

 border-top-color: @modal-footer-border;
 .box-shadow(none);
 background-color: @modal-footer-bg;
}
.modal-header .close {
 font-size: 32px;
}

/* wells */
.well {
 .border-radius(0);
}
.well h1, .well h2, .well h3, .well h4, .well h5, .well h6 {
 margin-top: 0;
}
.well h1, .well h2, .well h3 {
 line-height: 36px;
}


/* alerts */
.alert {
 font-size: 14px;
 .border-radius(0);

 .close {
	font-size: 16px;
 }
}
.alert-block p + p {
 margin-top: 10px;
}



/* pagination */
.pagination > li > a , .pager > li > a,
.pagination > li > span , .pager > li > span
{
 border-width: 1px;
 border-radius: 0 !important;
}
.pagination > li > a, .pager > li > a
{
 color: @pagination-color;
 background-color: @pagination-bg;
 margin: 0 -1px 0 0;
 border-color: @pagination-border;
}
.pagination > li > a:hover , .pager > li > a:hover {
 background-color: @pagination-bg-hover;
}
.pagination > li > a:focus , .pager > li > a:focus {
 background-color: spin(@pagination-bg-hover, 10%);
}

.pagination > li.disabled > a , .pagination > li.disabled > a:hover ,
.pager > li.disabled > a , .pager > li.disabled > a:hover {
 background-color: @pagination-bg-disabled;
 border-color: @pagination-border-disabled;
}

.pagination > li.active > a, .pagination > li.active > a:hover {
 background-color: @pagination-bg-active;
 border-color: @pagination-border-active;
 color: @pagination-color-active;
 text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}


.list-group-item {
	border-radius: 0 !important;
}

