{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#composer-lock-the-lock-file", "This file is @generated automatically"], "content-hash": "8b0f0ebece34c5a993d02d48c5b3df74", "packages": [{"name": "ezyang/htmlpurifier", "version": "v4.10.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "d85d39da4576a6934b72480be6978fb10c860021"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/d85d39da4576a6934b72480be6978fb10c860021", "reference": "d85d39da4576a6934b72480be6978fb10c860021", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"simpletest/simpletest": "^1.1"}, "type": "library", "autoload": {"psr-0": {"HTMLPurifier": "library/"}, "files": ["library/HTMLPurifier.composer.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "time": "2018-02-23T01:58:20+00:00"}, {"name": "ifsnop/mysqldump-php", "version": "v2.2", "source": {"type": "git", "url": "https://github.com/ifsnop/mysqldump-php.git", "reference": "6c1b84c5b05d862d15ba9ddf92c7d99a3d81e439"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ifsnop/mysqldump-php/zipball/6c1b84c5b05d862d15ba9ddf92c7d99a3d81e439", "reference": "6c1b84c5b05d862d15ba9ddf92c7d99a3d81e439", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "3.7.*", "squizlabs/php_codesniffer": "1.*"}, "type": "library", "autoload": {"psr-4": {"Ifsnop\\": "src/Ifsnop/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/ifsnop", "role": "Developer"}], "description": "This is a php version of linux's mysqldump in terminal \"$ mysqldump -u username -p...\"", "homepage": "https://github.com/ifsnop/mysqldump-php", "keywords": ["backup", "database", "dump", "export", "mysql", "mysqldump", "pdo", "sqlite"], "time": "2016-09-07T06:56:07+00:00"}, {"name": "psr/log", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2016-10-10T12:19:37+00:00"}, {"name": "wisembly/elephant.io", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/Wisembly/elephant.io.git", "reference": "e08dfbf261b759e76ebe403aeed0e4a50b613c7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Wisembly/elephant.io/zipball/e08dfbf261b759e76ebe403aeed0e4a50b613c7e", "reference": "e08dfbf261b759e76ebe403aeed0e4a50b613c7e", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/log": "~1.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"psr-4": {"ElephantIO\\": ["src/", "test/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://baptiste.xn--clavi-fsa.net/", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}, {"name": "Elephant.IO Community", "role": "Contributors :)"}], "description": "Send events to a websocket real time engine though PHP", "homepage": "http://elephant.io", "keywords": ["dialog", "nodejs", "real time", "websocket"], "time": "2016-10-25T07:52:01+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.4.0"}, "platform-dev": []}