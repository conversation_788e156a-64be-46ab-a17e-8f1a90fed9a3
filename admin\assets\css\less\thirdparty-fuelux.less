@wizard-step-border:#CED1D6;
@wizard-step-color:#546474;
@wizard-step-active-border:#5293C4;
@wizard-step-complete-color:#87BA21;
@wizard-step-title-color:#949EA7;
@wizard-step-active-title-color:#2B3D53;


/* spinner */
.ace-spinner {
  display:inline-block;

  .spinner-buttons {
	 min-width:18px;
	
	> .btn {
		.border-radius(0) !important;
		font-size:10px;
		padding:0;
		width:18px;
		height:14px;
		line-height:10px;
		
		&:first-child {
			margin-top:0;
		}
		
		> [class*="icon-"] {
			//font-size:10px;
			margin:0; padding:0;
		}
	}
	> button.btn {
		&:active { left:auto; top:auto; }
	}
  }
  
  .spinner-input {
	text-align:center;
	height:29px;
	line-height:28px;
	color:#777;
  }
  
  input[type=text].spinner-input.form-control {
    //ie8-9
	width: ~"auto\0/" !important;
	line-height: ~"1\0/" !important;
	padding-bottom: ~"4px\0/" !important; 
  }
}


/* touch spinner buttons */
.ace-spinner.touch-spinner .spinner-buttons {
   margin:0;
   font-size:0;
}
.ace-spinner.touch-spinner .spinner-buttons > .btn {
  height:29px; line-height:20px; width:24px;
  font-size:18px;
  display:inline-block;
  vertical-align:sub;
  margin:0 1px;
}
.ace-spinner.touch-spinner .spinner-buttons > .btn > [class*="icon-"]{
  vertical-align:middle;
  display:inline-block;
}








/* wizard */
.wizard-steps  {
	list-style:none;
	display:table;
	width:100%;
	padding:0;
	margin:0;
	position:relative;
	
	li {
		display:table-cell;
		text-align:center;
		width:1%;

		.step {
			border:5px solid @wizard-step-border;
			color:@wizard-step-color;
			font-size:15px;
			border-radius:100%;
			background-color:#FFF;
			 
			position:relative;
			z-index:2;
			display:inline-block;
			
			width:40px; height:40px;
			line-height:30px;
			text-align:center;
		}
		
		
		&:before {/* the line running through each step*/
			 display:block;
			 content:"";

			 width:100%;
			 height:1px; font-size:0; overflow:hidden;
			 
			 border-top:4px solid #CED1D6;
			 
			 position:relative; top:21px;
			 z-index:1;
		}
		
		&:last-child:before  {
			max-width:50%;
			width:50%;
		}
		&:first-child:before {
			max-width:51%;
			left:50%;
		}
		
		
		&.active, &.complete {
			&:before, .step {
				border-color:@wizard-step-active-border;
			}
		}
		
		&.complete {
			.step {
				cursor:default;
				color:#FFF;
				
				&:before {
					display:block;
					position:absolute;
					top:0; left:0; bottom:0; right:0;
					line-height:30px; text-align:center;
					border-radius:100%;
					 
					content:"\f00c";

					background-color:#FFF;
					z-index:3;
					 
					font-family:FontAwesome;
					font-size:17px;
					color:@wizard-step-complete-color;
				}
				
				.transition(~"transform ease 0.1s");
			}
			&:hover {
				.step {
					.transform(~"scale(1.1)");
					border-color:lighten(@wizard-step-active-border , 12%);
				}
				&:before {
					border-color:lighten(@wizard-step-active-border , 12%);
				}
			}
		}
		
		
		.title{
			display:block;
			margin-top:4px;
			max-width:100%;
			 
			color:@wizard-step-title-color;
			font-size:14px;
			 
			z-index:104;
			 
			text-align:center;
			table-layout:fixed;
			 
			word-wrap:break-word;
		}
		&.complete .title , &.active .title{
			color:@wizard-step-active-title-color;
		}

		
		
	}
	
}

.step-content .step-pane {
  display: none;
  min-height:200px;
  padding:4px 8px 12px;
}

.step-content .active {
  display: block;
}

.wizard-actions {
 text-align:right;
}

@media only screen and (max-width: @screen-xs-max) {
 .wizard-steps li .step {
	width:30px; height:30px; line-height:24px;
	border-width:3px;
 }
 .wizard-steps li:before  , .wizard-steps li:after {
	border-width:3px;
 }
 .wizard-steps li.complete .step:before{
	line-height:24px;
	font-size:13px;
 }
 .wizard-steps li:before {
	top:16px;
 }
 
 .step-content .step-pane {
	padding:4px 4px 6px;
 }
}










/* tree control */
@tree-border-color:#67B2DD;
.tree {
	padding-left:9px;
	overflow-x: hidden;	overflow-y: auto;
	position: relative;
	&:before {
		display:inline-block; content:""; 
		position:absolute; top:-20px; bottom:16px; left:0;
		border:1px dotted @tree-border-color;
		border-width: 0 0 0 1px;
	 
		z-index:1;
	}


	.tree-folder {
		width: auto;
		min-height: 20px;
		cursor: pointer;

		.tree-folder-header {
			position: relative;
			height: 20px;
			line-height:20px;
			&:hover {
				background-color: #F0F7FC;
			}
		}
	}
	
	.tree-folder .tree-folder-header .tree-folder-name , .tree-item .tree-item-name {
		display:inline;
		z-index:2;
	}
	.tree-folder .tree-folder-header > [class*="icon-"]:first-child , 
	.tree-item > [class*="icon-"]:first-child {
	  display:inline-block;
	  position:relative;
	  z-index:2;	  
	  top:-1px;
	}
	.tree-folder {
		.tree-folder-header {
			.tree-folder-name  {
				margin-left:2px;
			}
			> [class*="icon-"]:first-child {
				margin:-2px 0 0 -2px;
			}
		}
		&:last-child:after {
			display:inline-block; content:""; 
			position:absolute; z-index:1;
			top:15px; bottom:0; left:-15px;
			border-left:1px solid #FFF;
		}
		.tree-folder-content {
			margin-left: 23px;
			position:relative;
			&:before {
				display:inline-block; content:""; 
				position:absolute; z-index:1;
				top:-14px; bottom:16px; left:-14px;
				border:1px dotted @tree-border-color;
				border-width:0 0 0 1px;
			}
		}
	}



	.tree-item {
		position: relative;
		height: 20px;
		line-height:20px;
		cursor: pointer;
		&:hover {
			background-color: #F0F7FC;
		}
	
		.tree-item-name {
			margin-left:3px;
			> [class*="icon-"]:first-child {
				margin-right:3px;
			}
		}
		> [class*="icon-"]:first-child {
			margin-top:-1px;
		}
	}
	
	.tree-folder , .tree-item {
		position:relative;
		&:before {
			display:inline-block; content:""; 
			position:absolute;
			top:14px; left:-13px; width:18px;
			height:0;
			border-top:1px dotted @tree-border-color;
		 
			z-index:1;
		}
	}
	

	.tree-selected {
		background-color: rgba(98, 168, 209 , 0.1);
		color:#6398B0;
		&:hover {
			background-color: rgba(98, 168, 209 , 0.1);
		}
	}

	.tree-item  , .tree-folder {
		border:1px solid #FFF;
	}

	.tree-folder .tree-folder-header {
		border-radius:0;
	}
	.tree-item  , .tree-folder .tree-folder-header {
		margin:0;
		padding:5px;
		color:#4D6878;
		
		.box-sizing(content-box);
	}


	.tree-item > [class*="icon-"]:first-child {
		color:#F9E8CE;
		border:1px solid #CCC;
		width:13px; height:13px; line-height:13px;
		font-size:11px;
		text-align:center;
		border-radius:3px;
		
		.box-sizing(content-box);

		background-color: #FAFAFA;
		border: 1px solid #CCC;
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
	}

	.tree-selected > [class*="icon-"]:first-child {
		background-color: #F9A021;
		border-color: #F9A021;
		color: #FFF;
	}


	.icon-plus[class*="icon-"]:first-child , .icon-minus[class*="icon-"]:first-child {
		 border:1px solid #DDD;
		 vertical-align:middle;
		 height:11px; width:11px;
		 .box-sizing(content-box);
		 
		 text-align:center;

		 border:1px solid #8BAEBF;
		 line-height:10px;
		 background-color:#FFF;
		 
		 position:relative;
		 z-index:1;
	}
	.icon-plus[class*="icon-"]:first-child:before {
		display:block;
		content:"+";
		font-family:"Open Sans";
		font-size:16px;
		position:relative; z-index:1;
	}
	.icon-minus[class*="icon-"]:first-child:before {
		  content: "";
		  display:block;
		  width:7px; height:0;
		  border-top:1px solid #4D6878;
		  
		  position:absolute;
		  top:5px;
		  left:2px;
	}
	
	.tree-unselectable .tree-item > [class*="icon-"]:first-child {
		color:#5084A0;
		border:none;
		width:13px; height:13px; line-height:13px;
		font-size:10px;
		text-align:center;
		border-radius:0;

		background-color: transparent;
		border: none;
		box-shadow:none;
	}
	
	
	[class*="icon-"][class*="-down"] {
		transform:rotate(-45deg);
	}
	.icon-spin {
		height:auto;
	}
	.tree-loading {
		margin-left:36px;
	}
	
	img {
		display:inline;
		veritcal-align:middle;
	}
}





