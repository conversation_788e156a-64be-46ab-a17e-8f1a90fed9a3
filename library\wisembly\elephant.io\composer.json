{"name": "wisembly/elephant.io", "type": "library", "description": "Send events to a websocket real time engine though PHP", "keywords": ["Nodej<PERSON>", "WebSocket", "Dialog", "Real Time"], "license": "MIT", "homepage": "http://elephant.io", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://baptiste.xn--clavi-fsa.net/", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}, {"name": "Elephant.IO Community", "role": "Contributors :)"}], "require": {"php": ">=5.4.0", "psr/log": "~1.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "autoload": {"psr-4": {"ElephantIO\\": ["src/", "test/"]}}, "config": {"bin-dir": "bin"}, "minimum-stability": "dev", "prefer-stable": true}