/** buttons */
.btn {
 display:inline-block;

 color:#FFF !important;
 text-shadow:0 -1px 0 rgba(0, 0, 0, 0.25) !important;

 background-image:none !important;
 border:5px solid #FFF;
 border-radius:0;
 box-shadow:none !important;

 .transition(~"all ease .15s");

 cursor:pointer;

 vertical-align:middle;
 margin:0;

 position:relative;
 
}

.btn-lg {
	border-width:5px;
	line-height: 1.35;
    padding: 7px 16px;
}
.btn-sm {
	border-width:4px;
	font-size:13px;
	padding: 4px 9px;
	line-height:1.39;
}
.btn-xs {
	border-width:3px;
}
.btn-minier {
	padding: 0 4px;
	line-height: 18px;
	border-width: 2px;
	font-size: 12px;
}

button.btn:active  {
	top: 1px;
	left: 1px;
}





//button color
.btn-color(@color1, @color2) {
  & , &:focus {
	background-color:@color1 !important;
	border-color:@color1;
  }

  &:hover , .open &.dropdown-toggle {
	background-color:@color2 !important;
	border-color:@color1;
  }
  &.no-border:hover {
	border-color:@color2;
  }
  &.no-hover:hover {
	background-color:@color1 !important;
  }
  &.active {
	background-color: mix(@color1,@color2) !important;
	border-color: darken(mix(@color1,@color2),7%);
  }
  &.no-border.active {
    background-color: darken(mix(@color1,@color2),3%) !important;
	border-color: darken(mix(@color1,@color2),3%);
  }
  &.disabled, &[disabled], fieldset[disabled] & {
	&,
    &:hover,
    &:focus,
    &:active,
    &.active {
		background-color:@color1 !important;
		border-color:@color1;
	}
  }
}

.btn-color(@color-name) {
 @color1-name : ~`"btn-@{color-name}"`;
 @color2-name : ~`"btn-@{color-name}-hover"`;

 .btn-color(@@color1-name, @@color2-name);
}


.btn , .btn-default {
	.btn-color(~"default");
}
.btn-primary {
	.btn-color(~"primary");
}
.btn-info {
	.btn-color(~"info");
}
.btn-success {
	.btn-color(~"success");
}
.btn-warning {
	.btn-color(~"warning");
}
.btn-danger {
	.btn-color(~"danger");
}
.btn-inverse {
	.btn-color(~"inverse");
}
.btn-pink {
	.btn-color(~"pink");
}
.btn-purple {
	.btn-color(~"purple");
}
.btn-grey {
 .btn-color(~"grey");
}

.btn-yellow {
 .btn-color(~"yellow");
 color:@btn-yellow-color !important;
 text-shadow:0 -1px 0 rgba(255, 255, 255, 0.4) !important;
}

.btn-light {
 .btn-color(~"light");
 color:@btn-light-color !important;
 text-shadow:0 -1px 0 rgba(250, 250, 250, 0.25) !important;
}

.btn-light.btn-xs:after {
 left:-2px; right:-2px; top:-2px; bottom:-2px;
}
.btn-light.btn-sm:after {
 left:-4px; right:-4px; top:-4px; bottom:-4px;
}
.btn-light.btn-large:after {
 left:-6px; right:-6px; top:-6px; bottom:-6px;
}

.btn.btn-white {
 border-width:1px;
 border-color:#CCC;
 color:#444 !important;
 text-shadow:none !important;
 background-color:#FFF !important;

 &:hover, &:focus {
	background-color:#EBEBEB !important;
 }
 &.no-border {
	border-color: #CCC;
 }
}


.btn.disabled, .btn[disabled] {
 &.active, &:focus, &:active {
	outline:none;
 }
 &:active {
	top:0; left:0;
 }
}



/* active buttons */
.btn.active {
 color:@btn-active-color;
 &:after {
	display:inline-block;
	content:"";
	position:absolute;
	border-bottom:1px solid @btn-active-color;
	left:-4px; right:-4px; bottom:-4px;
 }
 &.btn-sm:after {
	left:-3px; right:-3px; bottom:-3px;
	//border-bottom-width:1px;
 }
 &.btn-large:after {
	left:-5px; right:-5px; bottom:-5px;
	//border-bottom-width:1px;
 }
 &.btn-xs:after ,  &.btn-minier:after {
	left:-1px; right:-1px; bottom:-2px;
	//border-bottom-width:1px;
 }
 &.btn-minier:after {
	bottom:-1px;
 }
 
  &.btn-yellow:after {
	border-bottom-color:@btn-yellow-active-border;
 }
 &.btn-light {	
	color:#515151;
	&:after {
		border-bottom-color:#B5B5B5;
	}
 }
}




/* icons inside buttons */
.btn {
	> [class*="icon-"] {
		display:inline;
		margin-right:4px;
		//min-width:12px;
		
		&.icon-on-right {
			margin-right:0;
			margin-left:4px;
		}
	}
	> .icon-only[class*="icon-"] {
		margin:0;
		vertical-align:middle;
		text-align:center;
		padding:0;
		//min-width:24px;
	}
}

.btn-large > [class*="icon-"] {
	margin-right:6px;
	
	&.icon-on-right {
		margin-right:0;
		margin-left:6px;
	}
}
.btn-sm > [class*="icon-"] {
	margin-right:3px;
	
	&.icon-on-right {
		margin-right:0;
		margin-left:3px;
	}
}
.btn-xs > [class*="icon-"] , &.btn-minier > [class*="icon-"] {
	margin-right:2px;

	&.icon-on-right {
		margin-right:0;
		margin-left:2px;
	}
}





.btn.btn-link {
  border:none !important;
  background:transparent none !important;
  color:@btn-link-color !important;
  text-shadow:none !important;
  padding:4px 12px !important;
  line-height:20px !important;
 
 &:hover {
	background:none !important;
	text-shadow:none !important;
 }
 &.active {
	background:none !important;
	text-decoration:underline;
	color:lighten(@btn-link-color , 6%) !important;
	&:after {
		display:none;
	}
 }
 &.disabled , &[disabled]{
	background:none;
	.opacity(0.65);
	&:hover {
		background:none !important;
		text-decoration:none !important;
	}
 }
}

.btn.btn-no-border {
 border-width:0 !important;
}






/* button groups */
.btn-group {
  > .btn {
	  & , + .btn {
		 margin:0 1px 0 0;
	  }
	  &:first-child {
		margin:0 1px 0 0;
	  }
	  &:first-child , &:last-child {
		border-radius:0;
	  }
	  
	  /* caret inside buttons */
	  > .caret {
		 margin-top:15px;
		 margin-left:1px;
		 border-width:5px;
		 border-top-color:#FFF;
	  }

	 &.btn-sm > .caret {
		margin-top:10px;
		border-width:4px;
	 }
	 &.btn-large > .caret {
		margin-top:18px;
		border-width:6px;
	 }
	 &.btn-xs > .caret {
		margin-top:9px;
		border-width:4px;
	 }
	 &.btn-minier > .caret {
		margin-top:7px;
		border-width:3px;
	 }
	 
	 /* dropdown toggle */
	 + .btn.dropdown-toggle {
		padding-right:3px;
		padding-left:3px;
	}
	+ .btn-large.dropdown-toggle {
		padding-right:4px;
		padding-left:4px;
	}

  }
  .dropdown-toggle {
	border-radius:0;
  }



  .btn-group-active-state(@left, @right, @bottom, @width) {/* the border under an active button in button groups */
		&.active:after {
			left:unit(@left, px); right:unit(@right, px); bottom:unit(@bottom, px);
			border-bottom-width:unit(@width, px);
		}
  }
   > .btn  , + .btn{
		margin:0 1px 0 0;
		border-width:3px;// !important;
		.btn-group-active-state(-2, -2, -2, 1);
   }
   > .btn-large  , + .btn-large{
		border-width:4px;// !important;
		.btn-group-active-state(-3, -3, -3, 1);
   }
   > .btn-sm  , + .btn-sm{
		border-width:2px;// !important;
		.btn-group-active-state(-1, -1, -1, 1);
   }
   > .btn-xs  , + .btn-xs{
		border-width:1px;// !important;
		.btn-group-active-state(0, 0, 0, 1);
   }
   > .btn-minier  , + .btn-minier{
		border-width:1px;// !important;
		.btn-group-active-state(0, 0, 0, 1);
   }
}

.btn-group > .btn {
  border-radius:0 !important;
}
.btn-group-vertical > .btn , .btn-group-vertical > .btn + .btn {
 margin:1px 0 0;
}
.btn-group-vertical > .btn:first-child {
	margin-right:0;
}










/* application buttons */
.btn.btn-app {
 display: inline-block;
 width: 100px;

 font-size: 18px;
 font-weight: normal;
 color: #FFF;
 
 text-align: center;
 text-shadow: 0 -1px -1px rgba(0,0,0,0.2) !important;
 
 border: none;
 border-radius: 12px;
 
 padding: 12px 0 8px;
 margin: 2px;
 
 line-height: 1.7;

 position: relative;
}


//button color
.btn-app-color(@color1, @color2, @percent) {
  & , &.no-hover:hover , &.disabled:hover {
	background: average(@color1, @color2) !important;
	#gradient > .vertical(@color1 , @color2) !important;
  }
  &:hover {
	background: average(darken(@color1,@percent), darken(@color2,@percent)) !important;
	#gradient > .vertical(darken(@color1,@percent) , darken(@color2,@percent)) !important;
  }
}

.btn-app-color(@color-name, @percent:10%) {
 @color1-name : ~`"btn-app-@{color-name}-1"`;
 @color2-name : ~`"btn-app-@{color-name}-2"`;
 
 .btn-app-color(@@color1-name, @@color2-name , @percent);
}

.btn-app, .btn-app.btn-default {
	.btn-app-color(~"default" , 8%);
}
.btn-app.btn-primary {
	.btn-app-color(~"primary");
}
.btn-app.btn-info {
	.btn-app-color(~"info");
}
.btn-app.btn-success {
	.btn-app-color(~"success");
}
.btn-app.btn-danger {
	.btn-app-color(~"danger");
}
.btn-app.btn-warning {
	.btn-app-color(~"warning");
}
.btn-app.btn-purple {
	.btn-app-color(~"purple");
}
.btn-app.btn-pink {
	.btn-app-color(~"pink");
}
.btn-app.btn-inverse {
	.btn-app-color(~"inverse");
}
.btn-app.btn-grey {
	.btn-app-color(~"grey" , 5%);
}

.btn.btn-app.btn-light {
 .btn-app-color(~"light" , 5%);

 color: @btn-app-light-color !important;
 text-shadow: 0 1px 1px #EEE !important;
}

.btn.btn-app.btn-yellow {
 .btn-app-color(~"yellow" , 5%);
 
 color:@btn-app-yellow-color !important;
 text-shadow:0 -1px 0 rgba(255, 255, 255, 0.4) !important;
}


.btn.btn-app {
	&.btn-sm {
		width:80px;
		font-size:16px;
		border-radius:10px ;
		
		line-height: 1.5;
	}
	&.btn-xs {
		width:64px;
		font-size:15px;
		border-radius:8px;
		padding-bottom:7px;
		padding-top:8px;
		
		line-height: 1.45;
	}
	
	

   > [class*=icon] {
		display:block;
		font-size:42px;
	 
		margin:0 0 4px;
		line-height:36px;
		min-width:0;
	 
		padding:0;
   }
   &.btn-sm > [class*=icon] {
		display:block;
		font-size:32px;
		line-height:30px;
		margin:0 0 3px;
	}
   &.btn-xs > [class*=icon] {
		display:block;
		font-size:24px;
		line-height:24px;
		margin:0;
	}
	
	&.no-radius {
		border-radius:0;
	}
	&.radius-4 {
		border-radius:4px;
	}
	
	/* badge & label inside buttons */
	> .badge , > .label {
		position:absolute !important;
		top:-2px; right:-2px;
		padding:1px 3px;
		text-align:center;
		font-size:12px;
		
		&.badge-left , &.label-left{
			right:auto;
			left:-2px;
		}
	}
	> .label {
		padding:1px 6px 3px;
		font-size:13px;
	}
	&.radius-4 , &.no-radius {
		> .badge {
			border-radius:3px;
			&.no-radius {// > .badge.no-radius
				border-radius:0;
			}
		}
	}
	
	/* active state */
	&.active {
		color:@btn-app-active;
		&:after {
			display:none;
		}
		&.btn-yellow {
			color:@btn-app-yellow-color;
			border-color:@btn-app-yellow-border;
		}
		&.btn-light {
			color:@btn-app-light-active;
		}
	}
}


.btn.btn-app {
 &.btn-light, &.btn-yellow {
	.box-shadow(~"0 0 0 1px rgba(0, 0, 0, 0.08) inset") !important;
 }
}



