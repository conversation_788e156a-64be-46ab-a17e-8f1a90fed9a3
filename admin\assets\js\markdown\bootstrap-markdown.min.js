!function(e){var c=function(g,f){this.$ns="bootstrap-markdown";this.$element=e(g);this.$editable={el:null,type:null,attrKeys:[],attrValues:[],content:null};this.$options=e.extend(true,{},e.fn.markdown.defaults,f);this.$oldContent=null;this.$isPreview=false;this.$editor=null;this.$textarea=null;this.$handler=[];this.$callback=[];this.$nextTab=[];this.showEditor()};c.prototype={constructor:c,__alterButtons:function(h,g){var i=this.$handler,f=(h=="all"),j=this;e.each(i,function(m,l){var n=true;if(f){n=false}else{n=l.indexOf(h)<0}if(n==false){g(j.$editor.find('button[data-handler="'+l+'"]'))}})},__buildButtons:function(j,g){var l,t=this.$ns,w=this.$handler,u=this.$callback;for(l=0;l<j.length;l++){var s,q=j[l];for(s=0;s<q.length;s++){var p,o=q[s].data,r=e("<div/>",{"class":"btn-group"});for(p=0;p<o.length;p++){var m=o[p],k="",v=t+"-"+m.name,n=m.btnText?m.btnText:"",f=m.btnClass?m.btnClass:"btn",h=m.tabIndex?m.tabIndex:"-1";if(m.toggle==true){k=' data-toggle="button"'}r.append('<button class="'+f+' btn-small" title="'+m.title+'" tabindex="'+h+'" data-provider="'+t+'" data-handler="'+v+'"'+k+'><i class="'+m.icon+'"></i> '+n+"</button>");w.push(v);u.push(m.callback)}g.append(r)}}return g},__setListener:function(){var g=typeof this.$textarea.attr("rows")!="undefined",f=this.$textarea.val().split("\n").length>5?this.$textarea.val().split("\n").length:"5",h=g?this.$textarea.attr("rows"):f;this.$textarea.attr("rows",h);this.$textarea.css("resize","none");this.$textarea.on("focus",e.proxy(this.focus,this)).on("keypress",e.proxy(this.keypress,this)).on("keyup",e.proxy(this.keyup,this));if(this.eventSupported("keydown")){this.$textarea.on("keydown",e.proxy(this.keydown,this))}this.$textarea.data("markdown",this)},__handle:function(j){var i=e(j.currentTarget),h=this.$handler,l=this.$callback,g=i.attr("data-handler"),k=h.indexOf(g),f=l[k];e(j.currentTarget).focus();f(this);if(g.indexOf("cmdSave")<0){this.$textarea.focus()}j.preventDefault()},showEditor:function(){var r=this,q,o=this.$ns,f=this.$element,p=f.css("height"),h=f.css("width"),j=this.$editable,t=this.$handler,s=this.$callback,u=this.$options,l=e("<div/>",{"class":"md-editor",click:function(){r.focus()}});if(this.$editor==null){var g=e("<div/>",{"class":"md-header"});if(u.buttons.length>0){g=this.__buildButtons(u.buttons,g)}if(u.additionalButtons.length>0){g=this.__buildButtons(u.additionalButtons,g)}l.append(g);if(f.is("textarea")){f.before(l);q=f;q.addClass("md-input");l.append(q)}else{var m=(typeof toMarkdown=="function")?toMarkdown(f.html()):f.html(),n=e.trim(m);q=e("<textarea/>",{"class":"md-input",val:n});l.append(q);j.el=f;j.type=f.prop("tagName").toLowerCase();j.content=f.html();e(f[0].attributes).each(function(){j.attrKeys.push(this.nodeName);j.attrValues.push(this.nodeValue)});f.replaceWith(l)}if(u.savable){var k=e("<div/>",{"class":"md-footer"}),i="cmdSave";t.push(i);s.push(u.onSave);k.append('<button class="btn btn-success" data-provider="'+o+'" data-handler="'+i+'"><i class="icon icon-white icon-ok"></i> Save</button>');l.append(k)}e.each(["height","width"],function(w,v){if(u[v]!="inherit"){if(jQuery.isNumeric(u[v])){l.css(v,u[v]+"px")}else{l.addClass(u[v])}}});this.$editor=l;this.$textarea=q;this.$editable=j;this.$oldContent=this.getContent();this.__setListener();this.$editor.attr("id",(new Date).getTime());this.$editor.on("click",'[data-provider="bootstrap-markdown"]',e.proxy(this.__handle,this))}else{this.$editor.show()}if(u.autofocus){this.$textarea.focus();this.$editor.addClass("active")}u.onShow(this);return this},showPreview:function(){var h=this.$options,i=h.onPreview(this),f=this.$textarea,k=f.next(),g=e("<div/>",{"class":"md-preview","data-provider":"markdown-preview"}),j;this.$isPreview=true;this.disableButtons("all").enableButtons("cmdPreview");if(typeof i=="string"){j=i}else{j=(typeof markdown=="object")?markdown.toHTML(f.val()):f.val()}g.html(j);if(k&&k.attr("class")=="md-footer"){g.insertBefore(k)}else{f.parent().append(g)}f.hide();g.data("markdown",this);return this},hidePreview:function(){this.$isPreview=false;var f=this.$editor.find('div[data-provider="markdown-preview"]');f.remove();this.enableButtons("all");this.$textarea.show();this.__setListener();return this},isDirty:function(){return this.$oldContent!=this.getContent()},getContent:function(){return this.$textarea.val()},setContent:function(f){this.$textarea.val(f);return this},findSelection:function(g){var j=this.getContent(),i;if(i=j.indexOf(g),i>=0&&g.length>0){var f=this.getSelection(),h;this.setSelection(i,i+g.length);h=this.getSelection();this.setSelection(f.start,f.end);return h}else{return null}},getSelection:function(){var f=this.$textarea[0];return(("selectionStart" in f&&function(){var g=f.selectionEnd-f.selectionStart;return{start:f.selectionStart,end:f.selectionEnd,length:g,text:f.value.substr(f.selectionStart,g)}})||function(){return null})()},setSelection:function(h,f){var g=this.$textarea[0];return(("selectionStart" in g&&function(){g.selectionStart=h;g.selectionEnd=f;return})||function(){return null})()},replaceSelection:function(g){var f=this.$textarea[0];return(("selectionStart" in f&&function(){f.value=f.value.substr(0,f.selectionStart)+g+f.value.substr(f.selectionEnd,f.value.length);f.selectionStart=f.value.length;return this})||function(){f.value+=g;return jQuery(f)})()},getNextTab:function(){if(this.$nextTab.length==0){return null}else{var f,g=this.$nextTab.shift();if(typeof g=="function"){f=g()}else{if(typeof g=="object"&&g.length>0){f=g}}return f}},setNextTab:function(i,g){if(typeof i=="string"){var h=this;this.$nextTab.push(function(){return h.findSelection(i)})}else{if(typeof i=="numeric"&&typeof g=="numeric"){var f=this.getSelection();this.setSelection(i,g);this.$nextTab.push(this.getSelection());this.setSelection(f.start,f.end)}}return},enableButtons:function(g){var f=function(h){h.removeAttr("disabled")};this.__alterButtons(g,f);return this},disableButtons:function(g){var f=function(h){h.attr("disabled","disabled")};this.__alterButtons(g,f);return this},eventSupported:function(f){var g=f in this.$element;if(!g){this.$element.setAttribute(f,"return;");g=typeof this.$element[f]==="function"}return g},keydown:function(f){this.suppressKeyPressRepeat=~e.inArray(f.keyCode,[40,38,9,13,27]);this.keyup(f)},keypress:function(f){if(this.suppressKeyPressRepeat){return}this.keyup(f)},keyup:function(i){var g=false;switch(i.keyCode){case 40:case 38:case 16:case 17:case 18:break;case 9:var f;if(f=this.getNextTab(),f!=null){var h=this;setTimeout(function(){h.setSelection(f.start,f.end)},500);g=true}else{var j=this.getSelection();if(j.start==j.end&&j.end==this.getContent().length){g=false}else{this.setSelection(this.getContent().length,this.getContent().length);g=true}}break;case 13:case 27:g=false;break;default:g=false}if(g){i.stopPropagation();i.preventDefault()}},focus:function(i){var f=this.$options,h=f.hideable,g=this.$editor;g.addClass("active");e(document).find(".md-editor").each(function(){if(e(this).attr("id")!=g.attr("id")){var j;if(j=e(this).find("textarea").data("markdown"),j==null){j=e(this).find('div[data-provider="markdown-preview"]').data("markdown")}if(j){j.blur()}}});return this},blur:function(m){var g=this.$options,l=g.hideable,j=this.$editor,h=this.$editable;if(j.hasClass("active")||this.$element.parent().length==0){j.removeClass("active");if(l){if(h.el!=null){var f=e("<"+h.type+"/>"),k=this.getContent(),i=(typeof markdown=="object")?markdown.toHTML(k):k;e(h.attrKeys).each(function(o,n){f.attr(h.attrKeys[o],h.attrValues[o])});f.html(i);j.replaceWith(f)}else{j.hide()}}g.onBlur(this)}return this}};var a=e.fn.markdown;e.fn.markdown=function(f){return this.each(function(){var i=e(this),h=i.data("markdown"),g=typeof f=="object"&&f;if(!h){i.data("markdown",(h=new c(this,g)))}})};e.fn.markdown.defaults={autofocus:false,hideable:false,savable:false,width:"inherit",height:"inherit",buttons:[[{name:"groupFont",data:[{name:"cmdBold",title:"Bold",icon:"icon icon-bold",callback:function(i){var f,j,g=i.getSelection(),h=i.getContent();if(g.length==0){f="strong text"}else{f=g.text}if(h.substr(g.start-2,2)=="**"&&h.substr(g.end,2)=="**"){i.setSelection(g.start-2,g.end+2);i.replaceSelection(f);j=g.start-2}else{i.replaceSelection("**"+f+"**");j=g.start+2}i.setSelection(j,j+f.length)}},{name:"cmdItalic",title:"Italic",icon:"icon icon-italic",callback:function(i){var f,j,g=i.getSelection(),h=i.getContent();if(g.length==0){f="emphasized text"}else{f=g.text}if(h.substr(g.start-1,1)=="*"&&h.substr(g.end,1)=="*"){i.setSelection(g.start-1,g.end+1);i.replaceSelection(f);j=g.start-1}else{i.replaceSelection("*"+f+"*");j=g.start+1}i.setSelection(j,j+f.length)}},{name:"cmdHeading",title:"Heading",icon:"icon icon-font",callback:function(i){var f,k,g=i.getSelection(),h=i.getContent(),j,l;if(g.length==0){f="heading text"}else{f=g.text}if((j=4,h.substr(g.start-j,j)=="### ")||(j=3,h.substr(g.start-j,j)=="###")){i.setSelection(g.start-j,g.end);i.replaceSelection(f);k=g.start-j}else{if(l=h.substr(g.start-1,1),!!l&&l!="\n"){i.replaceSelection("\n\n### "+f+"\n");k=g.start+6}else{i.replaceSelection("### "+f+"\n");k=g.start+4}}i.setSelection(k,k+f.length)}}]},{name:"groupLink",data:[{name:"cmdUrl",title:"URL/Link",icon:"icon icon-globe",callback:function(j){var f,k,g=j.getSelection(),i=j.getContent(),h;if(g.length==0){f="enter link description here"}else{f=g.text}bootbox.prompt("Insert Hyperlink",function(l){if(l!=null){j.replaceSelection("["+f+"]("+l+")");k=g.start+1;j.setSelection(k,k+f.length)}})}},{name:"cmdImage",title:"Image",icon:"icon icon-picture",callback:function(j){var f,k,g=j.getSelection(),i=j.getContent(),h;if(g.length==0){f="enter image description here"}else{f=g.text}bootbox.prompt("Insert Image Hyperlink",function(l){if(l!=null){j.replaceSelection("!["+f+"]("+l+' "enter image title here")');k=g.start+2;j.setNextTab("enter image title here");j.setSelection(k,k+f.length)}})}}]},{name:"groupMisc",data:[{name:"cmdList",title:"List",icon:"icon icon-list",callback:function(j){var f,k,g=j.getSelection(),h=j.getContent();if(g.length==0){f="list text here";j.replaceSelection("- "+f);k=g.start+2}else{if(g.text.indexOf("\n")<0){f=g.text;j.replaceSelection("- "+f);k=g.start+2}else{var i=[];i=g.text.split("\n");f=i[0];e.each(i,function(m,l){i[m]="- "+l});j.replaceSelection("\n\n"+i.join("\n"));k=g.start+4}}j.setSelection(k,k+f.length)}}]},{name:"groupUtil",data:[{name:"cmdPreview",toggle:true,title:"Preview",btnText:"Preview",btnClass:"btn btn-inverse",icon:"icon icon-white icon-search",callback:function(h){var f=h.$isPreview,g;if(f==false){h.showPreview()}else{h.hidePreview()}}}]}]],additionalButtons:[],onShow:function(f){},onPreview:function(f){},onSave:function(f){},onBlur:function(f){}};e.fn.markdown.Constructor=c;e.fn.markdown.noConflict=function(){e.fn.markdown=a;return this};var d=function(f){var g=f;if(g.data("markdown")){g.data("markdown").showEditor();return}g.markdown(g.data())};var b=function(h){var i=false,g,f=e(h.currentTarget);if((h.type=="focusin"||h.type=="click")&&f.length==1&&typeof f[0]=="object"){g=f[0].activeElement;if(!e(g).data("markdown")){if(typeof e(g).parent().parent().parent().attr("class")=="undefined"||e(g).parent().parent().parent().attr("class").indexOf("md-editor")<0){if(typeof e(g).parent().parent().attr("class")=="undefined"||e(g).parent().parent().attr("class").indexOf("md-editor")<0){i=true}}else{i=false}}if(i){e(document).find(".md-editor").each(function(){var k=e(g).parent();if(e(this).attr("id")!=k.attr("id")){var j;if(j=e(this).find("textarea").data("markdown"),j==null){j=e(this).find('div[data-provider="markdown-preview"]').data("markdown")}if(j){j.blur()}}})}h.stopPropagation()}};e(document).on("click.markdown.data-api",'[data-provide="markdown-editable"]',function(f){d(e(this));f.preventDefault()}).on("click",function(f){b(f)}).on("focusin",function(f){b(f)}).ready(function(){e('textarea[data-provide="markdown"]').each(function(){d(e(this))})})}(window.jQuery);