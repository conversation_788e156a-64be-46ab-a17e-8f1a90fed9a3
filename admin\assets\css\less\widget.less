
/* widget boxes */
.widget-box {
 padding:0;
 .box-shadow(none);
 margin:3px 0;
 
 border-bottom:1px solid #CCC; 
}

@media only screen and (max-width: @screen-xs-max) {
 .widget-box {
   margin-top:7px;
   margin-bottom:7px;
 }
}



.widget-header {
 .box-sizing(content-box);

 position:relative;
 min-height:38px;

 background:mix(#FFFFFF , #EEEEEE);
 #gradient > .vertical(#FFFFFF , #EEEEEE);

 color:@widget-header-color;

 border:1px solid #CCC;
 border-bottom:1px solid #DDD;

 padding-left:12px;
 
 &:before, &:after {
    content: "";
    display: table;
    line-height: 0;
 }
 &:after {
	clear:right;
 }
}





.collapsed {
	.widget-header { border-bottom-width:0; }
	.widget-body { display:none; }
}


.widget-header-flat {
 background:#F7F7F7;
 filter:progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
.widget-header-large {
 min-height:49px;
 padding-left:18px;
}
.widget-header-small {
 min-height:31px;
 padding-left:10px;
}


.widget-header > .widget-caption , .widget-header > :first-child {
 line-height:36px;
 padding:0;
 margin:0;
 display:inline;

 > [class*="icon-"] {
	margin-right:5px;
	font-weight:normal;
	//font-size:110%;
	display:inline-block;
 } 
}
.widget-header-large > .widget-caption , .widget-header-large > :first-child {
 line-height:48px;
}
.widget-header-small > .widget-caption , .widget-header-small > :first-child {
 line-height:30px;
}




.widget-toolbar {
  display:inline-block;
  padding:0 10px;
  line-height:37px;
  float:right;
  position:relative;

  .widget-header-large > & {
	line-height:48px;
  }
  .widget-header-small > & {
	line-height:29px;
  }

  &.no-padding {
	padding:0;
  }
  &.padding-5 {
	padding:0 5px;
  }
  
  &:before {/* border on left ot it! */
	display:inline-block;
	content:"";
	position:absolute; top:3px; bottom:3px; left:-1px;
	border:1px solid #D9D9D9;
	border-width:0 1px 0 0;
	
	.widget-header-large > & {
		top:6px; bottom:6px;
	}
	[class*="header-color-"] > & {
		border-color:#EEE;
	}
	.header-color-orange > & {
		border-color:#FEA;
	}
	.header-color-dark  > & {
		border-color:#222;
		box-shadow:-1px 0 0 rgba(255, 255, 255, 0.2) , inset 1px 0 0 rgba(255, 255, 255, 0.1);
	}
  }
  &.no-border:before {
	display:none;
  }
  
  label {
	display:inline-block;
	vertical-align:middle;
	margin-bottom:0;
  }
  
  /* widget toolbar basic buttons */
   > a {
	font-size:14px;
	margin:0 1px;
	display:inline-block;
	padding:0;
	line-height:24px;

	&:hover {
		text-decoration:none;
	}
	
	.widget-header-large > & {
		font-size:15px;
		margin:0 1px;
	}
  }


  /* toolbar buttons */
  > .btn {
	line-height:27px;
	margin-top:-2px;
	&.smaller {	line-height:26px; }
	 &.bigger { line-height:28px; }
  }
  > .btn-sm {
	line-height:24px;
	&.smaller {	line-height:23px; }
	 &.bigger { line-height:25px; }
  }
  > .btn-xs {
	line-height:22px;
	&.smaller {	line-height:21px; }
	 &.bigger {	line-height:23px; }
  }
  > .btn-minier {
	line-height:18px;
	&.smaller {	line-height:17px; }
	 &.bigger {	line-height:19px; }
  }
  > .btn-lg {
	line-height:36px;
	&.smaller { line-height:34px; }
	 &.bigger { line-height:38px; }
  }

}

.widget-toolbar-dark {
  background:#444;
}
.widget-toolbar-light {
  background: rgba(255, 255, 255, 0.85);
}






/* widget-toolbar buttons*/
.widget-toolbar > [data-action] {
  > [class*="icon-"] {
	margin-right:0;
  }
  &:focus {
	text-decoration:none;
  }
}

[class*="header-color-"] > .widget-toolbar {
	> [data-action] { text-shadow:0px 1px 1px rgba(0,0,0,0.2); }
	> [data-action="settings"] { color:#D3E4ED; }
	> [data-action="reload"] { color:#DEEAD3; }
	> [data-action="collapse"] { color:#E2E2E2; }
	> [data-action="close"] { color:#FFD9D5; }
}
.header-color-orange > .widget-toolbar {
	> [data-action] { text-shadow:none; }
	> [data-action="settings"] { color:#559AAB; }
	> [data-action="reload"] { color:#7CA362; }
	> [data-action="collapse"] { color:#777777; }
	> [data-action="close"] { color:#A05656; }
}
.widget-toolbar {
	& , .header-color-dark > & {
		> [data-action="settings"] { color:#99CADB; }
		> [data-action="reload"] { color:#ACD392; }
		> [data-action="collapse"] { color:#AAAAAA; }
		> [data-action="close"] { color:#E09E96; }
	}
}





.widget-body {
  border:1px solid #CCC;
  border-top:none;
  background-color:#FFF;
}
.widget-main {
  padding:12px;
}
.widget-main {
 .widget-paddingX (@index) when (@index >= 0) {
   &.padding-@{index} { padding:unit(@index,px); }
   .widget-paddingX(@index - 2);
  }
  .widget-paddingX(32);
  &.no-padding { padding:0; }
}


.widget-toolbar .progress {
 vertical-align:middle;
 display:inline-block;
 margin:0;
}



/* toolbar dropdowns */
.widget-toolbar > .dropdown , .widget-toolbar > .dropup{
	display:inline-block;
}
.widget-toolbar > .dropdown > .dropdown-menu:before , .dropdown-menu.dropdown-caret:before {
    border-bottom: 7px solid rgba(0, 0, 0, 0.2);
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    content: "";
    display: inline-block;
    left: 9px;
    position: absolute;
    top: -7px;
}
.widget-toolbar > .dropdown > .dropdown-menu:after, .dropdown-menu.dropdown-caret:after {
    border-bottom: 6px solid #FFFFFF;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    content: "";
    display: inline-block;
    left: 10px;
    position: absolute;
    top: -6px;
}

.widget-toolbar > .dropdown > .dropdown-menu.pull-right:before, .dropdown-menu.pull-right.dropdown-caret:before {
    left: auto;
	right:9px;
}
.widget-toolbar > .dropdown > .dropdown-menu.pull-right:after, .dropdown-menu.pull-right.dropdown-caret:after {
    left: auto;
	right:10px;
}





/* widget header colors */
.widget-header[class*="header-color-"] {
	color:#FFF;
	filter:progid:DXImageTransform.Microsoft.gradient(enabled=false);
}


//widget color
.widget-color(@color) {
  @widget-class:~`"header-color-@{color}"`;
  @widget-bg:~`"widget-@{color}"`;
  @widget-cl:@@widget-bg;

  .@{widget-class} {
	 background:@widget-cl;
	 border-color:@widget-cl;
	 + .widget-body  {
		border-color:@widget-cl;
	 }
  }
}
.widget-color(~"blue");
.widget-color(~"blue2");
.widget-color(~"blue3");
.widget-color(~"green");
.widget-color(~"green2");
.widget-color(~"green3");
.widget-color(~"red");
.widget-color(~"red2");
.widget-color(~"red3");
.widget-color(~"purple");
.widget-color(~"pink");

.header-color-orange {
  background:@widget-orange;
  border-color:@widget-orange-border;
  color:@widget-orange-txt !important;
  + .widget-body {
	border-color:@widget-orange-border;
  }
}

.header-color-dark {
  background:@widget-dark;
  border-color:lighten(@widget-dark, 2%);
  + .widget-body {
	border-color:lighten(@widget-dark, 15%);
  }
}

.header-color-grey {
  background:@widget-grey;
  border-color:lighten(@widget-grey, 8%);
  + .widget-body {
	border-color:lighten(@widget-grey, 15%);
  }
}


/* different borders */
.widget-box.light-border > [class*="header-color-"] + .widget-body {
 border-color:#D6D6D6 !important;
}
.widget-box.no-border {
  border-bottom:none;
  > .widget-body{
	border:none;
  }
}




/* transparent box */
.widget-box.transparent {
 border:none;
 box-shadow:none;
 
 > .widget-header {
	background:none;
	filter:progid:DXImageTransform.Microsoft.gradient(enabled=false);

	border:none;
	border-bottom:1px solid #DCE8F1;
	color:#4383B4;
 
	padding-left:3px;
 }
 > .widget-header-large {
	padding-left:5px;
 }
 > .widget-header-small {
	padding-left:1px;
 }

 .widget-body {
	border:none;
	background-color:transparent;
 }
 .widget-main {
	&.no-padding-left { padding-left:0; }
	&.no-padding-right { padding-right:0; }
	&.no-padding-top { padding-top:0; }
	&.no-padding-bottom { padding-bottom:0; }
 }
}





/* table in widget */
.widget-body .table {
  border-top:1px solid #E5E5E5;

  thead:first-child tr {
	  background:#FFF;
	  [class*="header-color-"] + & {
		 background : mix(#F8F8F8 , #ECECEC);
		 #gradient > .vertical(#F8F8F8 , #ECECEC);
	  }
  }
  &.table-bordered thead:first-child > tr {
		border-top:none;
  }
  
}

.widget-main.no-padding , .widget-main.padding-0 {
  .table {
	margin-bottom:0;
	border:none;
  }
  .table-bordered th:first-child,
  .table-bordered td:first-child {
	border-left-width:0;
  }

}

.transparent .widget-main , .widget-main.no-padding {
 .table-bordered > thead > tr > th:last-child, .table-bordered > tbody > tr > td:last-child, .table-bordered > tfoot > tr > td:last-child {
  border-right-width: 0 !important;
 }
}
.transparent .widget-main , .widget-main.no-padding {
.table-bordered > tbody > tr:last-child > td {
 border-bottom-width: 0 !important;
}
}
.table-bordered > thead.thin-border-bottom > tr > th, .table-bordered > thead.thin-border-bottom > tr > td {
	border-bottom-width: 1px;
}



/* elements in widget */
.widget-body .alert:last-child {
 margin-bottom:0;
}


/* tabs in widget */
.widget-main .tab-content {
 border:none;
}
.widget-toolbar > .nav-tabs {
  border-bottom:none;
  margin-bottom:0;
  position:relative; top:5px;
  
  > li > a {
	box-shadow:none;
  }
  > li:not(.active) > a{
	border-color:transparent; 
	background-color:transparent;
	&:hover {
		background-color:transparent;
	}
  }
  > li.active > a {
	background-color:#FFF;
	border-bottom-color:transparent;
	box-shadow:none;
  }
  
  
  .widget-header-small > & {
	 top:5px;
	 > li > a {
		line-height:16px;
		padding-top:6px; padding-bottom:6px;
	 }
	 > li.active > a {
		border-top-width:2px;
		margin-top:-1px;
	 }
  }
  
  .widget-header-large > & {
	 top:8px;
	 > li > a {
		line-height:22px;
		padding-top:9px; padding-bottom:9px;
	 }
  }
  
  
  [class*="header-color-"] > & {
	> li > a {
		border-color:transparent;
		background-color:transparent;
		color:#FFF;
		margin-right:1px;
		&:hover {
			background-color:#FFF;
			color:#555;
			border-top-color:#FFF;
		}
	}
	> li.active > a {
		background-color:#FFF;
		color:#555;
		border-top-width:1px;
		margin-top:0;
	}
  }
  .header-color-orange > &  > li > a{
		color:#855D10;
  }
  
  
  .transparent  & {
	> li > a {
		 color:#555;
		 background-color:transparent;
		 border-right:1px solid transparent;
		 border-left:1px solid transparent;
	}
	> li.active > a {
		border-top-color:#4C8FBD;
		border-right:1px solid #C5D0DC;
		border-left:1px solid #C5D0DC;
		background-color:#FFF;
		box-shadow:none;
	}
  }

  
}








/* toolbox */
.widget-toolbox {
	background-color:#EEE;

	&:first-child {/* the toolbox coming before content */
		padding:2px;
		border-bottom:1px solid #CCC;
	}
	&:last-child {/* the toolbox coming after content */
		padding:2px;
		border-top:1px solid #CCC;
		
		.transparent & {
			border:none;
			border-top:1px solid #CCC;
		}
	}

	> .btn-toolbar {
		margin:0;
		padding:0;
	}
	
	&.center {
		text-align:center;
	}
}



.widget-toolbox {
 .widget-paddingX (@index) when (@index >= 0) {
   &.padding-@{index} { padding:unit(@index,px); }
   .widget-paddingX(@index - 2);
  }
  .widget-paddingX(16);
}





/*when clicking refresh*/
.widget-box-overlay {
 position:absolute;
 top:0;bottom:0;right:0;left:0;
 background-color:rgba(0,0,0,0.3);
 
 z-index: 21;

 
 > [class*="icon-"] {
		position:absolute;
		top:15%;
		left:0; right:0; 
		text-align:center;
		
		.widget-box.collapsed  & {
			top: 5%;
		}
 }
 
 > .icon-spin {
	.animation-duration(1.2s);
 }

}



/* simple forms in widgetboxes */
.widget-main {
  > form {
	 margin-bottom:0;
	 .input-append , .input-prepend {
		margin-bottom:0;
	 }
  }
  
  &.no-padding, &.padding-0 {
	> form {
		> fieldset {
			padding:16px;
			+ .form-actions {
				padding:10px 0 12px;
			}
		}
		> .form-actions {
			margin:0;
			padding:10px 12px 12px;
		}
	}
  }
}

/* for dragging and dropping */
.widget-placeholder {
  border:2px dashed #D9D9D9;
}

