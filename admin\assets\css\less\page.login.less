/* login pages */

.login-container {
  width:375px;
  margin:0 auto;
}

.login-layout {
  background-color:#1D2024;
 .main-container:after {
	display:none;
 }

 .main-content {
	margin-left:0;
	min-height:100%;
	padding-left: 15px;
	padding-right:15px;
 }


 label {
	margin-bottom:11px;
 }

 
 .widget-box {
	visibility:hidden;
	position:absolute;	 
	overflow:hidden;
	width:100%;
	 
	border-bottom:none;
	box-shadow:none;
	padding:6px;
	background-color:#394557;
	 
	.transform(~"scale(0,1) translate(-150px)");	
	&.visible {
		visibility:visible;
		.transform(~"scale(1,1) translate(0)");

		.transition(~"all .3s ease");
		-o-transition: none;/* too slow */
		-webkit-transition: none;/* works in chrome but not in safari, never scales back to 1! */
	}
	
	.widget-main {
		padding:16px 36px 36px;
		background:#F7F7F7;
		form {
			margin:0;
		}
	}
	.widget-body .toolbar > div > a {
		font-size:15px;
		font-weight:400;
		text-shadow:1px 0px 1px rgba(0,0,0,0.25); 
	}
 }
 
 
}




.login-box {
  .forgot-password-link { color:#FE9; }
  .user-signup-link { color:#CF7; }
  
  .toolbar {
	background:#5090C1;
	border-top:2px solid #597597;
	> div {
		width:50%;
		display:inline-block;
		padding:9px 0 11px;

		&:first-child {//the first link
			float:left;
			text-align:left;
			> a {
				margin-left:11px;
			}
			
			+ div {//the next one
				float:right;
				text-align:right;
				> a {
					margin-right:11px;
				}
			}
		}
	}
  }
}




.forgot-box .toolbar {
 background:#C16050;
 border-top:2px solid #976559;
 padding:9px 18px;
}

.signup-box  .toolbar {
 background:#76B774;
 border-top:2px solid #759759;
 padding:9px 18px;
}

.forgot-box .back-to-login-link  , .signup-box .back-to-login-link{
 color:#FE9;
 font-size:14px;
 font-weight:bold;
 text-shadow:1px 0px 1px rgba(0,0,0,0.25); 
}





/* social login */
.login-layout .login-box .widget-main {
	padding-bottom:16px;
}
.login-box {

 .social-or-login {
	margin-top:4px;
	 
	position:relative; z-index:1;
	:first-child {
		display:inline-block;
		background: #F7F7F7;
		padding: 0 8px;
		color: #5090C1; font-size: 13px;
	}
	
	&:before {
		content:""; display:block;
		position:absolute; z-index:-1;
		top:50%; left:0; right:0;
		border-top:1px dotted #A6C4DB;
	}
 }

 .social-login {
	margin-top:12px;
	a {
		border-radius:100%;
		width:42px; height:42px; line-height:46px;
		padding:0;
		margin:0 1px;
		border:none;
		> [class*="icon-"] {
			font-size:24px;
			margin:0;
		}
	}
 }

}





/* loginbox */
@media only screen and (max-width: @screen-xs) {
.login-layout .widget-box .widget-main {
 padding:16px;
}
}
@media only screen and (max-width: @screen-xs) {
.login-container {
 width:98%;
}
.login-layout .widget-box {
 padding:0;
}

.login-box .toolbar > div {
 width:auto;
}
}
@media only screen and (max-width: @screen-xs-max) {
.login-layout .widget-box.visible {
 .transition(~"none");
}
}

