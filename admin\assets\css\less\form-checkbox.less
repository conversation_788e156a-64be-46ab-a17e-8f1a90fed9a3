//some checkbox & switch variables
@checkbox-color:#32A3CE;
@checkbox-border-color:#C8C8C8;
@checkbox-checked-border:#ADB8C0;
@checkbox-hover-border:#FF893C;
@checkbox2-bg:#F9A021;

@switch-checked-bg:#8AB2C9;
@switch-checked-border:#468FCC;
@switch4-bg:#8B9AA3;
@switch4-color:#5B6A73;
@switch4-checked-bg:#468FCC;

@switch6-checked-border:#B7D3E5;
@switch6-checked-bg:#FF893C;

@switch7-checked-bg:#468FCC;
@switch7-checked-border:#6FB3E0;


@switch-1-text:"ON\a0\a0\a0\a0\a0\a0\a0\a0\a0OFF";
@switch-2-text:"YES\a0\a0\a0\a0\a0\a0\a0\a0NO";
@switch-4-text:"ON\a0\a0\a0\a0\a0\a0\a0\a0\a0\a0\a0OFF";
@switch-5-text:"YES\a0\a0\a0\a0\a0\a0\a0\a0\a0\a0NO";
@switch-7-text:"OFF\a0\a0\a0\a0\a0\a0\a0\a0\a0\a0\a0ON";




/* Checkbox & Radio */
input[type=checkbox].ace , input[type=radio].ace {
	opacity:0;
	position:absolute;
	z-index:12;
	width:18px; height:18px;
	cursor:pointer;
	
	&:checked, &:focus {
		outline:none !important;
	}
	
	+ .lbl {
		position: relative; z-index:11;
		display:inline-block;
		margin:0;
		line-height:20px;

		min-height:18px;
		min-width:18px;
		font-weight:normal;
		
		
		.checkbox-paddings() {// a little paddings for .lbl
			 .checkbox-paddingX (@index) when (@index >= 0) {
				&.padding-@{index}::before {
					margin-right:unit(@index,px);
				}
			   .checkbox-paddingX(@index - 4);
			  }
			  .checkbox-paddingX(16);
		}
		.checkbox-paddings();


		&::before {
			font-family:fontAwesome; font-weight:normal;
			font-size: 12px; color:@checkbox-color;
			content:"\a0";
			display:inline-block;
			background-color: #FAFAFA;
			border: 1px solid @checkbox-border-color;
			box-shadow: 0 1px 2px rgba(0,0,0,0.05);/*, inset 0px -15px 10px -12px rgba(0,0,0,0.05);*/
			border-radius: 0;
			display: inline-block;
			text-align:center;
			
			vertical-align:middle;

			height:16px; line-height:14px;
			min-width:16px;	
			
			margin-right:1px;
		}
	}//.lbl
	
	&:checked + .lbl::before ,
	{
		display:inline-block;
		content: '\f00c';
		background-color: #F5F8FC;
		border-color:@checkbox-checked-border;
		box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05), inset 15px 10px -12px rgba(255,255,255,0.1);
	}
	&:hover + .lbl::before , + .lbl:hover::before {
		border-color:@checkbox-hover-border;
	}

	
	&:active , &:checked:active {
		+ .lbl::before {
			box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px 1px 3px rgba(0,0,0,0.1);
		}
	}
	
	
	&.ace-checkbox-2 + .lbl::before {
		box-shadow: none;
	}
	&.ace-checkbox-2:checked + .lbl::before {
		background-color: @checkbox2-bg;
		border-color: @checkbox2-bg;
		color: #FFF;
	}

	&:disabled + .lbl::before ,
	&[disabled] + .lbl::before ,
	&.disabled + .lbl::before {
		background-color:#DDD !important;
		border-color:#CCC !important;
		box-shadow:none !important;
		color:#BBB;
	}
}



/**
input[type=radio].ace + .lbl::before {
	border-radius:100%;
	font-size:34px;
	font-family: Helvetica, Arial, "Sans-Serif";//looks better but incosistent
	line-height:13px;
}
*/
input[type=radio].ace + .lbl::before {
	border-radius: 100%;
	font-size: 11px;
	font-family: FontAwesome;//let's use a fonts that's available everywhere
	text-shadow: 0 0 1px @checkbox-color;

	line-height: 15px;	
	height: 17px;
	min-width: 17px;
}
input[type=radio].ace:checked + .lbl::before {
	content:"\f111";
}







/* CSS3 on/off switches */
//use like <input type="checkbox" class="ace ace-switch" /> <span class="lbl"></span>
input[type=checkbox].ace.ace-switch {
  width:55px;
  height:25px;

	+ .lbl {
		margin:0 4px;
		min-height:24px;
		
		&::before {
			font-family:Arial, Helvetica, sans-serif;
			content:@switch-1-text;

			color:#999;
			font-weight:bold;
			font-size:11px;

			line-height:18px; line-height:21px\9;/*ie9*/
			height:20px;
			overflow:hidden;

			border-radius:12px;

			background-color: #F5F5F5;
			.box-shadow(~"inset 0px 2px 2px 0px rgba(0,0,0,.2)");

			border: 1px solid #CCC;

			text-align:left;
			float:left;
			padding:0;
			width:52px;
			text-indent:-19px; text-indent:~"-21px\9";
			margin-right:0;

			.transition(~"text-indent .4s ease");
		}
		
		&::after {
			font-family:Arial, Helvetica, sans-serif;
			content: '|||'; 
			font-size: 10px;
			font-weight:lighter;
			color:#D5D5D5;
			background-color:#FFF;
			
			text-shadow:-1px 0px 0 rgba(0, 0, 0, 0.15);
			text-align:center;
			border-radius:100%;
			
			width:22px; height:22px; line-height:20px;
			position: absolute;
			top: -2px; left: -3px;
			padding:0;

			.box-shadow(~"0px 1px 1px 1px rgba(0,0,0,.3)");
			//text-shadow:0px 1px 1px rgba(0,0,0,0.3) inset;

			.transition(~"left .4s ease");
		}
	}
	
	&:checked + .lbl {
		&::before {
			text-indent:9px;

			color:#FFF;
			background-color:@switch-checked-bg;
			border-color:@switch-checked-border;
		}
		&::after {
			left:34px;
			background-color:#FFF;
			color:@switch-checked-bg;
		}
	}
	
	
	&.ace-switch-2 + .lbl::before {
		content:@switch-2-text;
	}

	&.ace-switch-3 + .lbl::after {
		font-family:FontAwesome;
		font-size:13px; line-height:23px;
		content:"\f00d";
		top:-1px;
	}
	&.ace-switch-3:checked + .lbl::after {
		content:"\f00c";
	}
	
	/* switch style 4 & 5 */
	&.ace-switch-4 , &.ace-switch-5 {
		+ .lbl::before {
			content:@switch-4-text;
			font-family:Arial, Helvetica, sans-serif;
			font-weight:bolder;
			font-size:12px;
			line-height:23px; height:24px; overflow:hidden;
			line-height:25px\9;
			
			border-radius:12px;
			
			display:inline-block;
			background-color: @switch4-bg;
			border: 1px solid @switch4-bg;

			color:#FFF;
			width:56px;
			text-indent:-25px; text-indent:~"-28px\9";
			
			display: inline-block;
			position: relative;
			
			//margin-right:8px;
			
			box-shadow:none;
			
			.transition(~"all .4s ease");
		}
		
		+ .lbl::after {
			font-family:Helvetica, Arial, sans-serif;
			content: '|||'; text-shadow:-1px 0px 0 rgba(0, 0, 0, 0.2);
			font-size: 8px; font-weight:lighter;
			color:@switch4-bg;
			text-align:center;
			position: absolute;
			border-radius:12px;
			
			color:@switch4-color;
			top: 2px; left: 2px;
			width:20px; height:20px; line-height:18px;
			background-color:#FFF;

			.transition(~"all .4s ease");
		}

		
		&:checked + .lbl {
			&::before {
				text-indent:9px;
				background-color:@switch4-checked-bg;
				border-color:#468FCC;
			}
			&::after {
				left:34px;
				background-color:#FFF;
			}
		}
		
		
	}
	&.ace-switch-5 + .lbl::before {
		content:@switch-5-text;
	}
	&.ace-switch-5:checked + .lbl::before {
		text-indent:8px;
	}
	
	
	/* switch style 6 */
	&.ace-switch-6 {
		+ .lbl {
			position: relative;
			&::before {
				font-family:FontAwesome;
				content:"\f00d";
				text-shadow:0 -1px 0 rgba(0, 0, 0, 0.25);
				box-shadow:none;
				border:none;

				font-weight:lighter;
				font-size:16px;


				border-radius:12px;

				display:inline-block;
				background-color: #888;

				color:#F2F2F2;

				width:52px; height:22px; line-height:20px;
				text-indent:32px;

				.transition(~"background 0.1s ease");
			}
			&::after {
				content: ''; text-shadow:0 -1px 0 rgba(0, 0, 0, 0.25);

				position: absolute;
				top: 2px; left: 3px;
				
				border-radius:12px;
				box-shadow:0 -1px 0 rgba(0, 0, 0, 0.25);

				width:18px; height:18px;
				text-align:center;
				
				background-color:#F2F2F2;
				border:4px solid #F2F2F2;
				
				.transition(~"left 0.2s ease");
			}
		}
		
		&:checked + .lbl {
			&::before {
				content: "\f00c";
				text-indent:6px;

				color:#FFF;
				border-color:@switch6-checked-border;
				background-color:@switch6-checked-bg;
			}
			&::after {
				left:32px;

				background-color:#FFF;
				border:4px solid #FFF;
				text-shadow:0 -1px 0 rgba(0, 200, 0, 0.25);
			}
		}
	}
	
	
	&.ace-switch-7 {
		width:75px;

		+ .lbl {
			position: relative;
			&::before {
				content:@switch-7-text;

				font-weight:bolder;
				font-size:14px;
				line-height:20px;

				display:inline-block;
				background-color: #FFF;
				border: 2px solid #AAA;
				border-radius:0;
				box-shadow:none;

				color:#aaa;

				width:74px; height:26px; line-height:22px; overflow:hidden;
				text-indent:4px;
				
				display: inline-block;
				position: relative;
				
				//margin-right:8px;

				.transition(~"all 0.2s ease");
			}
			
			&::after {
				content: '\f00d';
				font-family:FontAwesome;
				font-size: 16px;

				position: absolute;
				top: 3px;
				left: 39px;
				width:32px; height:20px; line-height:18px;
				
				text-align:center;
				
				background-color:#aaa;
				color:#FFF;
				border-radius:0;
				box-shadow:none;
				
				.transition(~"all 0.2s ease-in-out");
			}
		}
		
		&:checked + .lbl {
			&::before {
				color:@switch7-checked-bg;
				background-color: #FFF;
				text-indent:-33px;
				border-color:@switch7-checked-border;
			}
			&::after {
				left:3px;
				content:'\f00c';
				background-color:@switch7-checked-bg;
				color: #FFF;
			}
		}

	}
}