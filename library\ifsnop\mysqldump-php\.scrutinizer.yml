before_commands:
    - 'composer install --dev --prefer-source'

checks:
    php: true

filter:
    excluded_paths: [vendor/*, tests/*]

tools:
    php_mess_detector:
        enabled: true
        config:
            controversial_rules:
                camel_case_method_name: false

    sensiolabs_security_checker: true

    # Analyzes the size and structure of a PHP project.
    php_pdepend: true
    php_loc:
        enabled: true

    # We recommend to use PHP Code Similarity Analyzer instead as it is
    # robust against code modifications and provides better targets for
    # refactoring.
    php_cpd: false
    php_sim: true

    # PHP Analyzer is our own analysis tool for PHP code. It’s latest
    # version is exclusively available through the hosted version on
    # scrutinizer-ci.com.
    php_analyzer: true
    php_code_sniffer:
        enabled: true
        config:
            standard: "PSR1"
            sniffs:
                generic:
                    naming_conventions:
                        camel_caps_function_name_sniff:  false
                    files:
                        one_class_per_file_sniff:  false
