!function(b){var a=function(k,s,i){var q=typeof s=="object";var f;this.startDate=moment().startOf("day");this.endDate=moment().startOf("day");this.minDate=false;this.maxDate=false;this.dateLimit=false;this.showDropdowns=false;this.showWeekNumbers=false;this.timePicker=false;this.timePickerIncrement=30;this.timePicker12Hour=true;this.ranges={};this.opens="right";this.buttonClasses=["btn","btn-sm"];this.applyClass="btn-success";this.cancelClass="btn-default";this.format="MM/DD/YYYY";this.separator=" - ";this.locale={applyLabel:"Apply",cancelLabel:"Cancel",fromLabel:"From",toLabel:"To",weekLabel:"W",customRangeLabel:"Custom Range",daysOfWeek:moment()._lang._weekdaysMin.slice(),monthNames:moment()._lang._monthsShort.slice(),firstDay:0};this.cb=function(){};this.element=b(k);if(this.element.hasClass("pull-right")){this.opens="left"}if(this.element.is("input")){this.element.on({click:b.proxy(this.show,this),focus:b.proxy(this.show,this)})}else{this.element.on("click",b.proxy(this.show,this))}f=this.locale;if(q){if(typeof s.locale=="object"){b.each(f,function(t,c){f[t]=s.locale[t]||c})}if(s.applyClass){this.applyClass=s.applyClass}if(s.cancelClass){this.cancelClass=s.cancelClass}}var d='<div class="daterangepicker dropdown-menu"><div class="calendar left"></div><div class="calendar right"></div><div class="ranges"><div class="range_inputs"><div class="daterangepicker_start_input" style="float: left"><label for="daterangepicker_start">'+this.locale.fromLabel+'</label><input class="input-mini" type="text" name="daterangepicker_start" value="" disabled="disabled" /></div><div class="daterangepicker_end_input" style="float: left; padding-left: 11px"><label for="daterangepicker_end">'+this.locale.toLabel+'</label><input class="input-mini" type="text" name="daterangepicker_end" value="" disabled="disabled" /></div><button class="'+this.applyClass+' applyBtn" disabled="disabled">'+this.locale.applyLabel+'</button>&nbsp;<button class="'+this.cancelClass+' cancelBtn">'+this.locale.cancelLabel+"</button></div></div></div>";this.container=b(d).appendTo("body");if(q){if(typeof s.format=="string"){this.format=s.format}if(typeof s.separator=="string"){this.separator=s.separator}if(typeof s.startDate=="string"){this.startDate=moment(s.startDate,this.format)}if(typeof s.endDate=="string"){this.endDate=moment(s.endDate,this.format)}if(typeof s.minDate=="string"){this.minDate=moment(s.minDate,this.format)}if(typeof s.maxDate=="string"){this.maxDate=moment(s.maxDate,this.format)}if(typeof s.startDate=="object"){this.startDate=moment(s.startDate)}if(typeof s.endDate=="object"){this.endDate=moment(s.endDate)}if(typeof s.minDate=="object"){this.minDate=moment(s.minDate)}if(typeof s.maxDate=="object"){this.maxDate=moment(s.maxDate)}if(typeof s.ranges=="object"){for(var m in s.ranges){var e=moment(s.ranges[m][0]);var j=moment(s.ranges[m][1]);if(this.minDate&&e.isBefore(this.minDate)){e=moment(this.minDate)}if(this.maxDate&&j.isAfter(this.maxDate)){j=moment(this.maxDate)}if((this.minDate&&j.isBefore(this.minDate))||(this.maxDate&&e.isAfter(this.maxDate))){continue}this.ranges[m]=[e,j]}var n="<ul>";for(var m in this.ranges){n+="<li>"+m+"</li>"}n+="<li>"+this.locale.customRangeLabel+"</li>";n+="</ul>";this.container.find(".ranges").prepend(n)}if(typeof s.dateLimit=="object"){this.dateLimit=s.dateLimit}if(typeof s.locale=="object"){if(typeof s.locale.firstDay=="number"){this.locale.firstDay=s.locale.firstDay;var l=s.locale.firstDay;while(l>0){this.locale.daysOfWeek.push(this.locale.daysOfWeek.shift());l--}}}if(typeof s.opens=="string"){this.opens=s.opens}if(typeof s.showWeekNumbers=="boolean"){this.showWeekNumbers=s.showWeekNumbers}if(typeof s.buttonClasses=="string"){this.buttonClasses=[s.buttonClasses]}if(typeof s.buttonClasses=="object"){this.buttonClasses=s.buttonClasses}if(typeof s.showDropdowns=="boolean"){this.showDropdowns=s.showDropdowns}if(typeof s.timePicker=="boolean"){this.timePicker=s.timePicker}if(typeof s.timePickerIncrement=="number"){this.timePickerIncrement=s.timePickerIncrement}if(typeof s.timePicker12Hour=="boolean"){this.timePicker12Hour=s.timePicker12Hour}}if(!this.timePicker){this.startDate=this.startDate.startOf("day");this.endDate=this.endDate.startOf("day")}var p=this.container;b.each(this.buttonClasses,function(c,t){p.find("button").addClass(t)});if(this.opens=="right"){var h=this.container.find(".calendar.left");var r=this.container.find(".calendar.right");h.removeClass("left").addClass("right");r.removeClass("right").addClass("left")}if(typeof s=="undefined"||typeof s.ranges=="undefined"){this.container.find(".calendar").show();this.move()}if(typeof i=="function"){this.cb=i}this.container.addClass("opens"+this.opens);if(!q||(typeof s.startDate=="undefined"&&typeof s.endDate=="undefined")){if(b(this.element).is("input[type=text]")){var g=b(this.element).val();var o=g.split(this.separator);var e,j;if(o.length==2){e=moment(o[0],this.format);j=moment(o[1],this.format)}if(e!=null&&j!=null){this.startDate=e;this.endDate=j}}}this.oldStartDate=this.startDate.clone();this.oldEndDate=this.endDate.clone();this.leftCalendar={month:moment([this.startDate.year(),this.startDate.month(),1,this.startDate.hour(),this.startDate.minute()]),calendar:[]};this.rightCalendar={month:moment([this.endDate.year(),this.endDate.month(),1,this.endDate.hour(),this.endDate.minute()]),calendar:[]};this.container.on("mousedown",b.proxy(this.mousedown,this));this.container.find(".calendar").on("click",".prev",b.proxy(this.clickPrev,this));this.container.find(".calendar").on("click",".next",b.proxy(this.clickNext,this));this.container.find(".ranges").on("click","button.applyBtn",b.proxy(this.clickApply,this));this.container.find(".ranges").on("click","button.cancelBtn",b.proxy(this.clickCancel,this));this.container.find(".ranges").on("click",".daterangepicker_start_input",b.proxy(this.showCalendars,this));this.container.find(".ranges").on("click",".daterangepicker_end_input",b.proxy(this.showCalendars,this));this.container.find(".calendar").on("click","td.available",b.proxy(this.clickDate,this));this.container.find(".calendar").on("mouseenter","td.available",b.proxy(this.enterDate,this));this.container.find(".calendar").on("mouseleave","td.available",b.proxy(this.updateView,this));this.container.find(".ranges").on("click","li",b.proxy(this.clickRange,this));this.container.find(".ranges").on("mouseenter","li",b.proxy(this.enterRange,this));this.container.find(".ranges").on("mouseleave","li",b.proxy(this.updateView,this));this.container.find(".calendar").on("change","select.yearselect",b.proxy(this.updateMonthYear,this));this.container.find(".calendar").on("change","select.monthselect",b.proxy(this.updateMonthYear,this));this.container.find(".calendar").on("change","select.hourselect",b.proxy(this.updateTime,this));this.container.find(".calendar").on("change","select.minuteselect",b.proxy(this.updateTime,this));this.container.find(".calendar").on("change","select.ampmselect",b.proxy(this.updateTime,this));this.element.on("keyup",b.proxy(this.updateFromControl,this));this.updateView();this.updateCalendars()};a.prototype={constructor:a,mousedown:function(c){c.stopPropagation()},updateView:function(){this.leftCalendar.month.month(this.startDate.month()).year(this.startDate.year());this.rightCalendar.month.month(this.endDate.month()).year(this.endDate.year());this.container.find("input[name=daterangepicker_start]").val(this.startDate.format(this.format));this.container.find("input[name=daterangepicker_end]").val(this.endDate.format(this.format));if(this.startDate.isSame(this.endDate)||this.startDate.isBefore(this.endDate)){this.container.find("button.applyBtn").removeAttr("disabled")}else{this.container.find("button.applyBtn").attr("disabled","disabled")}},updateFromControl:function(){if(!this.element.is("input")){return}if(!this.element.val().length){return}var d=this.element.val().split(this.separator);var e=moment(d[0],this.format);var c=moment(d[1],this.format);if(e==null||c==null){return}if(c.isBefore(e)){return}this.startDate=e;this.endDate=c;this.notify();this.updateCalendars()},notify:function(){this.updateView();this.cb(this.startDate,this.endDate)},move:function(){var c=b(this.container).find(".ranges").outerWidth();if(b(this.container).find(".calendar").is(":visible")){var d=24;c+=b(this.container).find(".calendar").outerWidth()*2+d}if(this.opens=="left"){this.container.css({top:this.element.offset().top+this.element.outerHeight(),right:b(window).width()-this.element.offset().left-this.element.outerWidth(),left:"auto","min-width":c});if(this.container.offset().left<0){this.container.css({right:"auto",left:9})}}else{this.container.css({top:this.element.offset().top+this.element.outerHeight(),left:this.element.offset().left,right:"auto","min-width":c});if(this.container.offset().left+this.container.outerWidth()>b(window).width()){this.container.css({left:"auto",right:0})}}},show:function(c){this.container.show();this.move();if(c){c.stopPropagation();c.preventDefault()}b(document).on("mousedown",b.proxy(this.hide,this));this.element.trigger("shown",{target:c.target,picker:this})},hide:function(c){this.container.hide();if(!this.startDate.isSame(this.oldStartDate)||!this.endDate.isSame(this.oldEndDate)){this.notify()}this.oldStartDate=this.startDate.clone();this.oldEndDate=this.endDate.clone();b(document).off("mousedown",this.hide);this.element.trigger("hidden",{picker:this})},enterRange:function(f){var c=f.target.innerHTML;if(c==this.locale.customRangeLabel){this.updateView()}else{var d=this.ranges[c];this.container.find("input[name=daterangepicker_start]").val(d[0].format(this.format));this.container.find("input[name=daterangepicker_end]").val(d[1].format(this.format))}},showCalendars:function(){this.container.find(".calendar").show();this.move()},updateInputText:function(){if(this.element.is("input")){this.element.val(this.startDate.format(this.format)+this.separator+this.endDate.format(this.format))}},clickRange:function(f){var c=f.target.innerHTML;if(c==this.locale.customRangeLabel){this.showCalendars()}else{var d=this.ranges[c];this.startDate=d[0];this.endDate=d[1];if(!this.timePicker){this.startDate.startOf("day");this.endDate.startOf("day")}this.leftCalendar.month.month(this.startDate.month()).year(this.startDate.year()).hour(this.startDate.hour()).minute(this.startDate.minute());this.rightCalendar.month.month(this.endDate.month()).year(this.endDate.year()).hour(this.endDate.hour()).minute(this.endDate.minute());this.updateCalendars();this.updateInputText();this.container.find(".calendar").hide();this.hide()}},clickPrev:function(d){var c=b(d.target).parents(".calendar");if(c.hasClass("left")){this.leftCalendar.month.subtract("month",1)}else{this.rightCalendar.month.subtract("month",1)}this.updateCalendars()},clickNext:function(d){var c=b(d.target).parents(".calendar");if(c.hasClass("left")){this.leftCalendar.month.add("month",1)}else{this.rightCalendar.month.add("month",1)}this.updateCalendars()},enterDate:function(f){var h=b(f.target).attr("data-title");var g=h.substr(1,1);var c=h.substr(3,1);var d=b(f.target).parents(".calendar");if(d.hasClass("left")){this.container.find("input[name=daterangepicker_start]").val(this.leftCalendar.calendar[g][c].format(this.format))}else{this.container.find("input[name=daterangepicker_end]").val(this.rightCalendar.calendar[g][c].format(this.format))}},clickDate:function(j){var k=b(j.target).attr("data-title");var l=k.substr(1,1);var g=k.substr(3,1);var d=b(j.target).parents(".calendar");if(d.hasClass("left")){var f=this.leftCalendar.calendar[l][g];var i=this.endDate;if(typeof this.dateLimit=="object"){var c=moment(f).add(this.dateLimit).startOf("day");if(i.isAfter(c)){i=c}}}else{var f=this.startDate;var i=this.rightCalendar.calendar[l][g];if(typeof this.dateLimit=="object"){var h=moment(i).subtract(this.dateLimit).startOf("day");if(f.isBefore(h)){f=h}}}d.find("td").removeClass("active");if(f.isSame(i)||f.isBefore(i)){b(j.target).addClass("active");this.startDate=f;this.endDate=i}else{if(f.isAfter(i)){b(j.target).addClass("active");this.startDate=f;this.endDate=moment(f).add("day",1).startOf("day")}}this.leftCalendar.month.month(this.startDate.month()).year(this.startDate.year());this.rightCalendar.month.month(this.endDate.month()).year(this.endDate.year());this.updateCalendars()},clickApply:function(c){this.updateInputText();this.hide()},clickCancel:function(c){this.startDate=this.oldStartDate;this.endDate=this.oldEndDate;this.updateView();this.updateCalendars();this.hide()},updateMonthYear:function(g){var h=b(g.target).closest(".calendar").hasClass("left");var f=this.container.find(".calendar.left");if(!h){f=this.container.find(".calendar.right")}var d=f.find(".monthselect").val();var c=f.find(".yearselect").val();if(h){this.leftCalendar.month.month(d).year(c)}else{this.rightCalendar.month.month(d).year(c)}this.updateCalendars()},updateTime:function(h){var j=b(h.target).closest(".calendar").hasClass("left");var g=this.container.find(".calendar.left");if(!j){g=this.container.find(".calendar.right")}var d=parseInt(g.find(".hourselect").val());var i=parseInt(g.find(".minuteselect").val());if(this.timePicker12Hour){var f=g.find(".ampmselect").val();if(f=="PM"&&d<12){d+=12}if(f=="AM"&&d==12){d=0}}if(j){var k=this.startDate;k.hour(d);k.minute(i);this.startDate=k;this.leftCalendar.month.hour(d).minute(i)}else{var c=this.endDate;c.hour(d);c.minute(i);this.endDate=c;this.rightCalendar.month.hour(d).minute(i)}this.updateCalendars()},updateCalendars:function(){this.leftCalendar.calendar=this.buildCalendar(this.leftCalendar.month.month(),this.leftCalendar.month.year(),this.leftCalendar.month.hour(),this.leftCalendar.month.minute(),"left");this.rightCalendar.calendar=this.buildCalendar(this.rightCalendar.month.month(),this.rightCalendar.month.year(),this.rightCalendar.month.hour(),this.rightCalendar.month.minute(),"right");this.container.find(".calendar.left").html(this.renderCalendar(this.leftCalendar.calendar,this.startDate,this.minDate,this.maxDate));this.container.find(".calendar.right").html(this.renderCalendar(this.rightCalendar.calendar,this.endDate,this.startDate,this.maxDate));this.container.find(".ranges li").removeClass("active");var c=true;var e=0;for(var d in this.ranges){if(this.timePicker){if(this.startDate.isSame(this.ranges[d][0])&&this.endDate.isSame(this.ranges[d][1])){c=false;this.container.find(".ranges li:eq("+e+")").addClass("active")}}else{if(this.startDate.format("YYYY-MM-DD")==this.ranges[d][0].format("YYYY-MM-DD")&&this.endDate.format("YYYY-MM-DD")==this.ranges[d][1].format("YYYY-MM-DD")){c=false;this.container.find(".ranges li:eq("+e+")").addClass("active")}}e++}if(c){this.container.find(".ranges li:last").addClass("active")}},buildCalendar:function(o,q,h,g,p){var c=moment([q,o,1]);var m=moment(c).subtract("month",1).month();var l=moment(c).subtract("month",1).year();var r=moment([l,m]).daysInMonth();var e=c.day();var k=[];for(var j=0;j<6;j++){k[j]=[]}var n=r-e+this.locale.firstDay+1;if(n>r){n-=7}if(e==this.locale.firstDay){n=r-6}var f=moment([l,m,n,h,g]);for(var j=0,d=0,s=0;j<42;j++,d++,f=moment(f).add("day",1)){if(j>0&&d%7==0){d=0;s++}k[s][d]=f}return k},renderDropdowns:function(h,g,c){var k=h.month();var f='<select class="monthselect">';var d=false;var p=false;for(var e=0;e<12;e++){if((!d||e>=g.month())&&(!p||e<=c.month())){f+="<option value='"+e+"'"+(e===k?" selected='selected'":"")+">"+this.locale.monthNames[e]+"</option>"}}f+="</select>";var j=h.year();var i=(c&&c.year())||(j+5);var o=(g&&g.year())||(j-50);var n='<select class="yearselect">';for(var l=o;l<=i;l++){n+='<option value="'+l+'"'+(l===j?' selected="selected"':"")+">"+l+"</option>"}n+="</select>";return f+n},renderCalendar:function(m,k,j,d){var p='<div class="calendar-date">';p+='<table class="table-condensed">';p+="<thead>";p+="<tr>";if(this.showWeekNumbers){p+="<th></th>"}if(!j||j.isBefore(m[1][1])){p+='<th class="prev available"><i class="icon-arrow-left glyphicon glyphicon-arrow-left"></i></th>'}else{p+="<th></th>"}var f=this.locale.monthNames[m[1][1].month()]+m[1][1].format(" YYYY");if(this.showDropdowns){f=this.renderDropdowns(m[1][1],j,d)}p+='<th colspan="5" style="width: auto">'+f+"</th>";if(!d||d.isAfter(m[1][1])){p+='<th class="next available"><i class="icon-arrow-right glyphicon glyphicon-arrow-right"></i></th>'}else{p+="<th></th>"}p+="</tr>";p+="<tr>";if(this.showWeekNumbers){p+='<th class="week">'+this.locale.weekLabel+"</th>"}b.each(this.locale.daysOfWeek,function(s,i){p+="<th>"+i+"</th>"});p+="</tr>";p+="</thead>";p+="<tbody>";for(var r=0;r<6;r++){p+="<tr>";if(this.showWeekNumbers){p+='<td class="week">'+m[r][0].week()+"</td>"}for(var g=0;g<7;g++){var o="available ";o+=(m[r][g].month()==m[1][1].month())?"":"off";if((j&&m[r][g].isBefore(j))||(d&&m[r][g].isAfter(d))){o=" off disabled "}else{if(m[r][g].format("YYYY-MM-DD")==k.format("YYYY-MM-DD")){o+=" active ";if(m[r][g].format("YYYY-MM-DD")==this.startDate.format("YYYY-MM-DD")){o+=" start-date "}if(m[r][g].format("YYYY-MM-DD")==this.endDate.format("YYYY-MM-DD")){o+=" end-date "}}else{if(m[r][g]>=this.startDate&&m[r][g]<=this.endDate){o+=" in-range ";if(m[r][g].isSame(this.startDate)){o+=" start-date "}if(m[r][g].isSame(this.endDate)){o+=" end-date "}}}}var q="r"+r+"c"+g;p+='<td class="'+o.replace(/\s+/g," ").replace(/^\s?(.*?)\s?$/,"$1")+'" data-title="'+q+'">'+m[r][g].date()+"</td>"}p+="</tr>"}p+="</tbody>";p+="</table>";p+="</div>";if(this.timePicker){p+='<div class="calendar-time">';p+='<select class="hourselect">';var e=0;var h=23;var c=k.hour();if(this.timePicker12Hour){e=1;h=12;if(c>=12){c-=12}if(c==0){c=12}}for(var l=e;l<=h;l++){if(l==c){p+='<option value="'+l+'" selected="selected">'+l+"</option>"}else{p+='<option value="'+l+'">'+l+"</option>"}}p+="</select> : ";p+='<select class="minuteselect">';for(var l=0;l<60;l+=this.timePickerIncrement){var n=l;if(n<10){n="0"+n}if(l==k.minute()){p+='<option value="'+l+'" selected="selected">'+n+"</option>"}else{p+='<option value="'+l+'">'+n+"</option>"}}p+="</select> ";if(this.timePicker12Hour){p+='<select class="ampmselect">';if(k.hour()>=12){p+='<option value="AM">AM</option><option value="PM" selected="selected">PM</option>'}else{p+='<option value="AM" selected="selected">AM</option><option value="PM">PM</option>'}p+="</select>"}p+="</div>"}return p}};b.fn.daterangepicker=function(d,c){this.each(function(){var e=b(this);if(!e.data("daterangepicker")){e.data("daterangepicker",new a(e,d,c))}});return this}}(window.jQuery);