@table-header-bg:@widget-blue;
@table-sort-active:#307ECC;
@table-sort-hover:#547EA8;

@table-labels-grad-color1:#F8F8F8;
@table-labels-grad-color2:#ECECEC;


/* tables */
th, td , .table-bordered {
	border-radius:0 !important;
}

.table {
  thead tr {
	 color:#707070;
	 font-weight:normal;

	 background:#F2F2F2;
	 #gradient > .vertical(@table-labels-grad-color1 , @table-labels-grad-color2);
	 
	 th {
		border-color:#DDD;
		//border:1px solid #DDD;
		//border-width:0 1px;
		font-weight:bold;

		[class*="icon-"]:first-child {
			margin-right:2px;
		}
		&:first-child {
			border-left-color:#F1F1F1;
		}
		&:left-child {
			border-right-color:#F1F1F1;
		}
	 }
  }
 
  &.table-bordered thead tr  th {
	//border-width:0 0 0 1px;
	//border-color:#DDD;
	vertical-align:middle;

	&:first-child {
		border-left-color:#DDD;
	}
  }


  //&.table-hover tbody tr:hover td{
	//background-color: #F1F1F1;
  //}
}

th , td {
   &.center {
	text-align:center;
   }

   .lbl {
	margin-bottom:0;
	&:only-child {
		vertical-align:top;
	}
   }
}



.table-header {
	background-color:@table-header-bg;
	color:#FFF;
	font-size:14px;
	line-height:38px;
	padding-left:12px;
	margin-bottom:1px;
}
.table-header .close {
   margin-right: 8px;
   margin-top: 0;
   .opacity(0.45);
   &:hover {
	.opacity(0.75);
   }
}






/* dataTables */
.dataTables_length {
  //margin-left:8px;
  select {
	width:70px;
	height:25px;
	padding:2px 3px;
  }
  label {
	font-weight:normal;
  }
}
.dataTables_filter {
 // margin-right:8px;
  text-align:right;
  input[type=text] {
	width:125px;
	height:18px;
	line-height:18px;
	.box-sizing(content-box);
	padding:4px 6px;
  }
  label {
	font-weight:normal;
  }
}

.dataTables_info {
 // margin:0 0 0 12px;
  font-size:14px;
}

.dataTables_paginate {
  text-align:right;
  .pagination {
	margin:0 12px;
  }
}





.dataTables_wrapper {
  label {
	display:inline-block;
	font-size:13px;
  }
  input[type=text] , select {
	margin-bottom:0 !important;
	margin:0 4px;
  }

  .row {
	margin:0;
	
	&:first-child {
		padding-top:12px;
		padding-bottom:12px;
		
		background-color:#EFF3F8;

		+ .dataTable {
			border-top:1px solid #DDD;
			border-bottom:1px solid #DDD;
		}
	}
	&:last-child {
		border-top:1px solid #DDD;
		padding-top:12px;
		padding-bottom:12px;
		
		background-color:#EFF3F8;
		border-bottom:1px solid #DDD;
	}
  }

}




.dataTable {
  margin-bottom:0;
  th {
    &[class*=sort] {
		cursor:pointer;
		&:after {
			content:"\f0dc";
			display:inline-block;
			color:#555;
			font-family:FontAwesome; font-size:13px; font-weight:normal;
			float:right;
			margin-right:4px;
			position:relative;
		}
		
		&:hover {
			color:@table-sort-hover;
		}
	}

	&[class*=sorting_] {
		color:@table-sort-active;
	}
	
	&.sorting_desc , &.sorting_asc {
		#gradient > .vertical(#EFF3F8 , #E3E7ED);
    }
	
	&.sorting_desc:after {
		content:"\f0dd";
		top:-6px;
		color:@table-sort-active;
	}
	&.sorting_asc:after {
		content:"\f0de";
		top:4px;
		color:@table-sort-active;
	}

	&.sorting_disabled {
		cursor:inherit;
		&:after { display:none; }
	}

  }

}

