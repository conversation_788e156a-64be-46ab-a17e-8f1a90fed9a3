(function(K){var e=K.Markdown=function e(R){switch(typeof R){case"undefined":this.dialect=e.dialects.Gruber;break;case"object":this.dialect=R;break;default:if(R in e.dialects){this.dialect=e.dialects[R]}else{throw new Error("Unknown Markdown dialect '"+String(R)+"'")}break}this.em_state=[];this.strong_state=[];this.debug_indent=""};K.parse=function(T,R){var S=new e(R);return S.toTree(T)};K.toHTML=function u(U,T,S){var R=K.toHTMLTree(U,T,S);return K.renderJsonML(R)};K.toHTMLTree=function w(S,W,U){if(typeof S==="string"){S=this.parse(S,W)}var T=C(S),R={};if(T&&T.references){R=T.references}var V=i(S,R,U);q(V);return V};function f(){return"Markdown.mk_block( "+uneval(this.toString())+", "+uneval(this.trailing)+", "+uneval(this.lineNumber)+" )"}function H(){var R=require("util");return"Markdown.mk_block( "+R.inspect(this.toString())+", "+R.inspect(this.trailing)+", "+R.inspect(this.lineNumber)+" )"}var g=e.mk_block=function(U,S,R){if(arguments.length==1){S="\n\n"}var T=new String(U);T.trailing=S;T.inspect=H;T.toSource=f;if(R!=undefined){T.lineNumber=R}return T};function G(S){var T=0,R=-1;while((R=S.indexOf("\n",R+1))!==-1){T++}return T}e.prototype.split_blocks=function z(T,W){var U=/([\s\S]+?)($|\n(?:\s*\n|$)+)/g,V=[],R;var S=1;if((R=/^(\s*\n)/.exec(T))!=null){S+=G(R[0]);U.lastIndex=R[0].length}while((R=U.exec(T))!==null){V.push(g(R[1],R[2],S));S+=G(R[0])}return V};e.prototype.processBlock=function M(W,V){var S=this.dialect.block,R=S.__order__;if("__call__" in S){return S.__call__.call(this,W,V)}for(var U=0;U<R.length;U++){var T=S[R[U]].call(this,W,V);if(T){if(!A(T)||(T.length>0&&!(A(T[0])))){this.debug(R[U],"didn't return a proper array")}return T}}return[]};e.prototype.processInline=function h(R){return this.dialect.inline.__call__.call(this,String(R))};e.prototype.toTree=function I(T,S){var U=T instanceof Array?T:this.split_blocks(T);var V=this.tree;try{this.tree=S||this.tree||["markdown"];U:while(U.length){var R=this.processBlock(U.shift(),U);if(!R.length){continue U}this.tree.push.apply(this.tree,R)}return this.tree}finally{if(S){this.tree=V}}};e.prototype.debug=function(){var R=Array.prototype.slice.call(arguments);R.unshift(this.debug_indent);if(typeof print!=="undefined"){print.apply(print,R)}if(typeof console!=="undefined"&&typeof console.log!=="undefined"){console.log.apply(null,R)}};e.prototype.loop_re_over_block=function(U,V,T){var S,R=V.valueOf();while(R.length&&(S=U.exec(R))!=null){R=R.substr(S[0].length);T.call(this,S)}return R};e.dialects={};e.dialects.Gruber={block:{atxHeader:function s(T,S){var R=T.match(/^(#{1,6})\s*(.*?)\s*#*\s*(?:\n|$)/);if(!R){return undefined}var U=["header",{level:R[1].length}];Array.prototype.push.apply(U,this.processInline(R[2]));if(R[0].length<T.length){S.unshift(g(T.substr(R[0].length),T.trailing,T.lineNumber+2))}return[U]},setextHeader:function x(T,S){var R=T.match(/^(.*)\n([-=])\2\2+(?:\n|$)/);if(!R){return undefined}var V=(R[2]==="=")?1:2;var U=["header",{level:V},R[1]];if(R[0].length<T.length){S.unshift(g(T.substr(R[0].length),T.trailing,T.lineNumber+2))}return[U]},code:function l(W,V){var T=[],U=/^(?: {0,3}\t| {4})(.*)\n?/,S;if(!W.match(U)){return undefined}block_search:do{var R=this.loop_re_over_block(U,W.valueOf(),function(X){T.push(X[1])});if(R.length){V.unshift(g(R,W.trailing));break block_search}else{if(V.length){if(!V[0].match(U)){break block_search}T.push(W.trailing.replace(/[^\n]/g,"").substring(2));W=V.shift()}else{break block_search}}}while(true);return[["code_block",T.join("\n")]]},horizRule:function P(T,S){var R=T.match(/^(?:([\s\S]*?)\n)?[ \t]*([-_*])(?:[ \t]*\2){2,}[ \t]*(?:\n([\s\S]*))?$/);if(!R){return undefined}var U=[["hr"]];if(R[1]){U.unshift.apply(U,this.processBlock(R[1],[]))}if(R[3]){S.unshift(g(R[3]))}return U},lists:(function(){var U="[*+-]|\\d+\\.",S=/[*+-]/,aa=/\d+\./,Y=new RegExp("^( {0,3})("+U+")[ \t]+"),T="(?: {0,3}\\t| {4})";function V(ab){return new RegExp("(?:^("+T+"{0,"+ab+"} {0,3})("+U+")\\s+)|(^"+T+"{0,"+(ab-1)+"}[ ]{0,4})")}function R(ab){return ab.replace(/ {0,3}\t/g,"    ")}function Z(ac,ai,ah,ad){if(ai){ac.push(["para"].concat(ah));return}var ab=ac[ac.length-1] instanceof Array&&ac[ac.length-1][0]=="para"?ac[ac.length-1]:ac;if(ad&&ac.length>1){ah.unshift(ad)}for(var af=0;af<ah.length;af++){var ag=ah[af],ae=typeof ag=="string";if(ae&&ab.length>1&&typeof ab[ab.length-1]=="string"){ab[ab.length-1]+=ag}else{ab.push(ag)}}}function W(ah,ag){var af=new RegExp("^("+T+"{"+ah+"}.*?\\n?)*$"),ae=new RegExp("^"+T+"{"+ah+"}","gm"),ad=[];while(ag.length>0){if(af.exec(ag[0])){var ac=ag.shift(),ab=ac.replace(ae,"");ad.push(g(ab,ac.trailing,ac.lineNumber))}break}return ad}function X(ae,ad,ab){var ag=ae.list;var af=ag[ag.length-1];if(af[1] instanceof Array&&af[1][0]=="para"){return}if(ad+1==ab.length){af.push(["para"].concat(af.splice(1)))}else{var ac=af.pop();af.push(["para"].concat(af.splice(1)),ac)}}return function(ag,am){var an=ag.match(Y);if(!an){return undefined}function ae(ax){var ay=S.exec(ax[2])?["bulletlist"]:["numberlist"];af.push({list:ay,indent:ax[1]});return ay}var af=[],ar=ae(an),ad,al=false,aw=[af[0].list],ap;loose_search:while(true){var ab=ag.split(/(?=\n)/);var at="";tight_search:for(var au=0;au<ab.length;au++){var ac="",ao=ab[au].replace(/^\n/,function(ax){ac=ax;return""});var aj=V(af.length);an=ao.match(aj);if(an[1]!==undefined){if(at.length){Z(ad,al,this.processInline(at),ac);al=false;at=""}an[1]=R(an[1]);var ah=Math.floor(an[1].length/4)+1;if(ah>af.length){ar=ae(an);ad.push(ar);ad=ar[1]=["listitem"]}else{var ak=false;for(ap=0;ap<af.length;ap++){if(af[ap].indent!=an[1]){continue}ar=af[ap].list;af.splice(ap+1);ak=true;break}if(!ak){ah++;if(ah<=af.length){af.splice(ah);ar=af[ah-1].list}else{ar=ae(an);ad.push(ar)}}ad=["listitem"];ar.push(ad)}ac=""}if(ao.length>an[0].length){at+=ac+ao.substr(an[0].length)}}if(at.length){Z(ad,al,this.processInline(at),ac);al=false;at=""}var aq=W(af.length,am);if(aq.length>0){c(af,X,this);ad.push.apply(ad,this.toTree(aq,[]))}var ai=am[0]&&am[0].valueOf()||"";if(ai.match(Y)||ai.match(/^ /)){ag=am.shift();var av=this.dialect.block.horizRule(ag,am);if(av){aw.push.apply(aw,av);break}c(af,X,this);al=true;continue loose_search}break}return aw}})(),blockquote:function L(W,U){if(!W.match(/^>/m)){return undefined}var Y=[];if(W[0]!=">"){var S=W.split(/\n/),V=[];while(S.length&&S[0][0]!=">"){V.push(S.shift())}W=S.join("\n");Y.push.apply(Y,this.processBlock(V.join("\n"),[]))}while(U.length&&U[0][0]==">"){var R=U.shift();W=new String(W+W.trailing+R);W.trailing=R.trailing}var T=W.replace(/^> ?/gm,""),X=this.tree;Y.push(this.toTree(T,["blockquote"]));return Y},referenceDefn:function J(V,U){var T=/^\s*\[(.*?)\]:\s*(\S+)(?:\s+(?:(['"])(.*?)\3|\((.*?)\)))?\n?/;if(!V.match(T)){return undefined}if(!C(this.tree)){this.tree.splice(1,0,{})}var S=C(this.tree);if(S.references===undefined){S.references={}}var R=this.loop_re_over_block(T,V,function(W){if(W[2]&&W[2][0]=="<"&&W[2][W[2].length-1]==">"){W[2]=W[2].substring(1,W[2].length-1)}var X=S.references[W[1].toLowerCase()]={href:W[2]};if(W[4]!==undefined){X.title=W[4]}else{if(W[5]!==undefined){X.title=W[5]}}});if(R.length){U.unshift(g(R,V.trailing))}return[]},para:function D(S,R){return[["para"].concat(this.processInline(S))]}}};e.dialects.Gruber.inline={__oneElement__:function d(W,S,V){var R,T,X=0;S=S||this.dialect.inline.__patterns__;var U=new RegExp("([\\s\\S]*?)("+(S.source||S)+")");R=U.exec(W);if(!R){return[W.length,W]}else{if(R[1]){return[R[1].length,R[1]]}}var T;if(R[2] in this.dialect.inline){T=this.dialect.inline[R[2]].call(this,W.substr(R.index),R,V||[])}T=T||[R[2].length,R[2]];return T},__call__:function r(V,T){var R=[],S;function U(W){if(typeof W=="string"&&typeof R[R.length-1]=="string"){R[R.length-1]+=W}else{R.push(W)}}while(V.length>0){S=this.dialect.inline.__oneElement__.call(this,V,T,R);V=V.substr(S.shift());c(S,U)}return R},"]":function(){},"}":function(){},"\\":function t(R){if(R.match(/^\\[\\`\*_{}\[\]()#\+.!\-]/)){return[2,R[1]]}else{return[1,"\\"]}},"![":function m(T){var R=T.match(/^!\[(.*?)\][ \t]*\([ \t]*(\S*)(?:[ \t]+(["'])(.*?)\3)?[ \t]*\)/);if(R){if(R[2]&&R[2][0]=="<"&&R[2][R[2].length-1]==">"){R[2]=R[2].substring(1,R[2].length-1)}R[2]=this.dialect.inline.__call__.call(this,R[2],/\\/)[0];var S={alt:R[1],href:R[2]||""};if(R[4]!==undefined){S.title=R[4]}return[R[0].length,["img",S]]}R=T.match(/^!\[(.*?)\][ \t]*\[(.*?)\]/);if(R){return[R[0].length,["img_ref",{alt:R[1],ref:R[2].toLowerCase(),original:R[0]}]]}return[2,"!["]},"[":function n(ab){var Y=String(ab);var W=e.DialectHelpers.inline_until_char.call(this,ab.substr(1),"]");if(!W){return[1,"["]}var Z=1+W[0],S=W[1],X,aa;ab=ab.substr(Z);var U=ab.match(/^\s*\([ \t]*(\S+)(?:[ \t]+(["'])(.*?)\2)?[ \t]*\)/);if(U){var R=U[1];Z+=U[0].length;if(R&&R[0]=="<"&&R[R.length-1]==">"){R=R.substring(1,R.length-1)}if(!U[3]){var T=1;for(var V=0;V<R.length;V++){switch(R[V]){case"(":T++;break;case")":if(--T==0){Z-=R.length-V;R=R.substring(0,V)}break}}}R=this.dialect.inline.__call__.call(this,R,/\\/)[0];aa={href:R||""};if(U[3]!==undefined){aa.title=U[3]}X=["link",aa].concat(S);return[Z,X]}U=ab.match(/^\s*\[(.*?)\]/);if(U){Z+=U[0].length;aa={ref:(U[1]||String(S)).toLowerCase(),original:Y.substr(0,Z)};X=["link_ref",aa].concat(S);return[Z,X]}if(S.length==1&&typeof S[0]=="string"){aa={ref:S[0].toLowerCase(),original:Y.substr(0,Z)};X=["link_ref",aa,S[0]];return[Z,X]}return[1,"["]},"<":function v(S){var R;if((R=S.match(/^<(?:((https?|ftp|mailto):[^>]+)|(.*?@.*?\.[a-zA-Z]+))>/))!=null){if(R[3]){return[R[0].length,["link",{href:"mailto:"+R[3]},R[3]]]}else{if(R[2]=="mailto"){return[R[0].length,["link",{href:R[1]},R[1].substr("mailto:".length)]]}else{return[R[0].length,["link",{href:R[1]},R[1]]]}}}return[1,"<"]},"`":function y(S){var R=S.match(/(`+)(([\s\S]*?)\1)/);if(R&&R[2]){return[R[1].length+R[2].length,["inlinecode",R[3]]]}else{return[1,"`"]}},"  \n":function O(R){return[3,["linebreak"]]}};function F(R,U){var T=R+"_state",V=R=="strong"?"em_state":"strong_state";function S(W){this.len_after=W;this.name="close_"+U}return function(ad,ac){if(this[T][0]==U){this[T].shift();return[ad.length,new S(ad.length-U.length)]}else{var W=this[V].slice(),ab=this[T].slice();this[T].unshift(U);var Y=this.processInline(ad.substr(U.length));var Z=Y[Y.length-1];var X=this[T].shift();if(Z instanceof S){Y.pop();var aa=ad.length-Z.len_after;return[aa,[R].concat(Y)]}else{this[V]=W;this[T]=ab;return[U.length,U]}}}}e.dialects.Gruber.inline["**"]=F("strong","**");e.dialects.Gruber.inline.__=F("strong","__");e.dialects.Gruber.inline["*"]=F("em","*");e.dialects.Gruber.inline._=F("em","_");e.buildBlockOrder=function(T){var R=[];for(var S in T){if(S=="__order__"||S=="__call__"){continue}R.push(S)}T.__order__=R};e.buildInlinePatterns=function(V){var U=[];for(var S in V){if(S.match(/^__.*__$/)){continue}var R=S.replace(/([\\.*+?|()\[\]{}])/g,"\\$1").replace(/\n/,"\\n");U.push(S.length==1?R:"(?:"+R+")")}U=U.join("|");V.__patterns__=U;var T=V.__call__;V.__call__=function(X,W){if(W!=undefined){return T.call(this,X,W)}else{return T.call(this,X,U)}}};e.DialectHelpers={};e.DialectHelpers.inline_until_char=function(V,U){var T=0,R=[];while(true){if(V[T]==U){T++;return[T,R]}if(T>=V.length){return null}var S=this.dialect.inline.__oneElement__.call(this,V.substr(T));T+=S[0];R.push.apply(R,S.slice(1))}};e.subclassDialect=function(T){function R(){}R.prototype=T.block;function S(){}S.prototype=T.inline;return{block:new R(),inline:new S()}};e.buildBlockOrder(e.dialects.Gruber.block);e.buildInlinePatterns(e.dialects.Gruber.inline);e.dialects.Maruku=e.subclassDialect(e.dialects.Gruber);e.dialects.Maruku.processMetaHash=function k(U){var V=o(U),R={};for(var S=0;S<V.length;++S){if(/^#/.test(V[S])){R.id=V[S].substring(1)}else{if(/^\./.test(V[S])){if(R["class"]){R["class"]=R["class"]+V[S].replace(/./," ")}else{R["class"]=V[S].substring(1)}}else{if(/\=/.test(V[S])){var T=V[S].split(/\=/);R[T[0]]=T[1]}}}}return R};function o(T){var V=T.split(""),U=[""],R=false;while(V.length){var S=V.shift();switch(S){case" ":if(R){U[U.length-1]+=S}else{U.push("")}break;case"'":case'"':R=!R;break;case"\\":S=V.shift();default:U[U.length-1]+=S;break}}return U}e.dialects.Maruku.block.document_meta=function j(W,T){if(W.lineNumber>1){return undefined}if(!W.match(/^(?:\w+:.*\n)*\w+:.*$/)){return undefined}if(!C(this.tree)){this.tree.splice(1,0,{})}var V=W.split(/\n/);for(p in V){var R=V[p].match(/(\w+):\s*(.*)$/),S=R[1].toLowerCase(),U=R[2];this.tree[1][S]=U}return[]};e.dialects.Maruku.block.block_meta=function E(Y,V){var U=Y.match(/(^|\n) {0,3}\{:\s*((?:\\\}|[^\}])*)\s*\}$/);if(!U){return undefined}var T=this.dialect.processMetaHash(U[2]);var X;if(U[1]===""){var W=this.tree[this.tree.length-1];X=C(W);if(typeof W==="string"){return undefined}if(!X){X={};W.splice(1,0,X)}for(a in T){X[a]=T[a]}return[]}var S=Y.replace(/\n.*$/,""),R=this.processBlock(S,[]);X=C(R[0]);if(!X){X={};R[0].splice(1,0,X)}for(a in T){X[a]=T[a]}return R};e.dialects.Maruku.block.definition_list=function N(T,W){var Y=/^((?:[^\s:].*\n)+):\s+([\s\S]+)$/,X=["dl"],V;if((S=T.match(Y))){var R=[T];while(W.length&&Y.exec(W[0])){R.push(W.shift())}for(var Z=0;Z<R.length;++Z){var S=R[Z].match(Y),U=S[1].replace(/\n$/,"").split(/\n/),aa=S[2].split(/\n:\s+/);for(V=0;V<U.length;++V){X.push(["dt",U[V]])}for(V=0;V<aa.length;++V){X.push(["dd"].concat(this.processInline(aa[V].replace(/(\n)\s+/,"$1"))))}}}else{return undefined}return[X]};e.dialects.Maruku.inline["{:"]=function B(Y,W,U){if(!U.length){return[2,"{:"]}var V=U[U.length-1];if(typeof V==="string"){return[2,"{:"]}var S=Y.match(/^\{:\s*((?:\\\}|[^\}])*)\s*\}/);if(!S){return[2,"{:"]}var X=this.dialect.processMetaHash(S[1]),R=C(V);if(!R){R={};V.splice(1,0,R)}for(var T in X){R[T]=X[T]}return[S[0].length,""]};e.buildBlockOrder(e.dialects.Maruku.block);e.buildInlinePatterns(e.dialects.Maruku.inline);var A=Array.isArray||function(R){return Object.prototype.toString.call(R)=="[object Array]"};var c;if(Array.prototype.forEach){c=function(S,R,T){return S.forEach(R,T)}}else{c=function(S,R,U){for(var T=0;T<S.length;T++){R.call(U||S,S[T],T,S)}}}function C(R){return A(R)&&R.length>1&&typeof R[1]==="object"&&!(A(R[1]))?R[1]:undefined}K.renderJsonML=function(T,R){R=R||{};R.root=R.root||false;var S=[];if(R.root){S.push(Q(T))}else{T.shift();if(T.length&&typeof T[0]==="object"&&!(T[0] instanceof Array)){T.shift()}while(T.length){S.push(Q(T.shift()))}}return S.join("\n\n")};function b(R){return R.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;")}function Q(W){if(typeof W==="string"){return b(W)}var R=W.shift(),T={},U=[];if(W.length&&typeof W[0]==="object"&&!(W[0] instanceof Array)){T=W.shift()}while(W.length){U.push(arguments.callee(W.shift()))}var V="";for(var S in T){V+=" "+S+'="'+b(T[S])+'"'}if(R=="img"||R=="br"||R=="hr"){return"<"+R+V+"/>"}else{return"<"+R+V+">"+U.join("")+"</"+R+">"}}function i(Z,W,Y){var T;Y=Y||{};var V=Z.slice(0);if(typeof Y.preprocessTreeNode==="function"){V=Y.preprocessTreeNode(V,W)}var X=C(V);if(X){V[1]={};for(T in X){V[1][T]=X[T]}X=V[1]}if(typeof V==="string"){return V}switch(V[0]){case"header":V[0]="h"+V[1].level;delete V[1].level;break;case"bulletlist":V[0]="ul";break;case"numberlist":V[0]="ol";break;case"listitem":V[0]="li";break;case"para":V[0]="p";break;case"markdown":V[0]="html";if(X){delete X.references}break;case"code_block":V[0]="pre";T=X?2:1;var R=["code"];R.push.apply(R,V.splice(T));V[T]=R;break;case"inlinecode":V[0]="code";break;case"img":V[1].src=V[1].href;delete V[1].href;break;case"linebreak":V[0]="br";break;case"link":V[0]="a";break;case"link_ref":V[0]="a";var S=W[X.ref];if(S){delete X.ref;X.href=S.href;if(S.title){X.title=S.title}delete X.original}else{return X.original}break;case"img_ref":V[0]="img";var S=W[X.ref];if(S){delete X.ref;X.src=S.href;if(S.title){X.title=S.title}delete X.original}else{return X.original}break}T=1;if(X){for(var U in V[1]){T=2}if(T===1){V.splice(T,1)}}for(;T<V.length;++T){V[T]=arguments.callee(V[T],W,Y)}return V}function q(S){var R=C(S)?2:1;while(R<S.length){if(typeof S[R]==="string"){if(R+1<S.length&&typeof S[R+1]==="string"){S[R]+=S.splice(R+1,1)[0]}else{++R}}else{arguments.callee(S[R]);++R}}}})((function(){if(typeof exports==="undefined"){window.markdown={};return window.markdown}else{return exports}})());