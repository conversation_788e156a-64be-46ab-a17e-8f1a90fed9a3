/* a few ie specific changes */
.sidebar {
 *left:0;
}
.sidebar:before {
 display: none;
}


.ace-nav > li.white-opaque {
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#CCFFFFFF', endColorstr='#CCFFFFFF',GradientType=0 );
}
.ace-nav > li.dark-opaque {
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#33000000', endColorstr='#33000000',GradientType=0 );
}

.infobox > .infobox-icon > [class*="icon-"]:before {
  color: #FFF;
}
.infobox-dark > .badge {
 filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#33FFFFFF', endColorstr='#33FFFFFF',GradientType=0 ) !important;
}
.widget-box-overlay {
 filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#55000000', endColorstr='#55000000',GradientType=0 ) !important;
}
.widget-toolbar-light {
 filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#D8FFFFFF', endColorstr='#D8FFFFFF',GradientType=0 ) !important;
}

input[type=checkbox].ace , input[type=radio].ace {
	position:static;
	width:auto; height:auto;
	z-index:auto;
}
input[type=checkbox].ace + .lbl, input[type=radio].ace + .lbl{
	min-height:auto;
	min-width:auto;
}
input[type=checkbox].ace.ace-switch  {
	width:auto;
}
input[type=checkbox].ace.ace-switch + .lbl {
	margin:0;
	min-height:auto;
}
input[type=checkbox].ace.ace-switch-7 {
	width:auto;
}
.ace-file-input input[type=file] {
 /*must be visible and on top for ie8/9 to actually work */
 width:100%; height:30px;
 position:absolute;
 z-index:1;
 filter:alpha(opacity=0);
 cursor:pointer;
}
.ace-file-input input[type=file]:hover + .file-label {
 border-color: #f59942;
}
.ace-file-multiple input[type=file] {
 height:100%;
}
.ace-file-input .remove {
 z-index:2;
}
.ace-file-input .file-label.selected .file-name {
 width:50%; /* prevent long filename lay over button in IE */
}
.ace-file-multiple .file-label.selected .file-name {
 width:auto;
}

.wizard-steps li:first-child:before {
 max-width:100%;
 left:0;
}

.login-layout .widget-box {
 display:none;
 visibility:visible;
 position:static;
}

.login-layout .widget-box.visible {
 display:block;
}
.pricing-box-small:hover {
  zoom:1.04;
  left:-1px; top:-3px;
}


.ace-thumbnails > li > a > img{
 width:auto !important;
}
.ace-thumbnails > li > :first-child > .text{
 display:none;
}
.ace-thumbnails > li:hover > :first-child  > .text{
 display:block;
}
.ace-thumbnails > li > .tools{
 filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#8C000000', endColorstr='#8C000000', GradientType=0) !important;
}
.ace-thumbnails > li > :first-child > .text{
 filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#8C000000', endColorstr='#8C000000', GradientType=0) !important;
}


#cboxLoadingGraphic > [class*="icon-"] {
 display:inline-block;
 background:#FFF url('images/loading.gif') no-repeat center; 
}
#cboxLoadingGraphic > [class*="icon-"]:before {
 display:none;
}

.widget-box-overlay > [class*="icon-"] {
  display:inline-block;
  width:24px; height:24px;
  margin-left:46%;
  background:transparent url('images/loading.gif') no-repeat center; 
}
.widget-box-overlay > [class*="icon-"]:before {
  display:none;
}


.btn.btn-app.btn-light {
  border: 1px solid #D9D9D9;
}
.btn.btn-app.btn-yellow {
  border: 1px solid #FEE188;
}




.grid3 {
 width:31%;
}
.grid4 {
 width:23%;
}

.itemdiv.dialogdiv > .body:before{
 display:none;
}

.fc-event-hori, .fc-event-vert {
 border:none !important;
}


[class*="tab-color-"] .nav-tabs > li > a > [class*="icon-"]:first-child{
 color:#666 !important;
}


.dropdown-preview > .dropdown-menu {
 *width: 180px;
}

/*jquery ui*/
.ui-datepicker , .ui-autocomplete , .ui-menu{
  border:1px solid #CCC;
}
.ui-widget-overlay  {
  filter:alpha(opacity=100) !important;
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#44000000', endColorstr='#44000000',GradientType=0 ) !important;
}


.message-content {
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#CCFFFFFF', endColorstr='#CCFFFFFF',GradientType=0 ) !important;
}



/* semi transparent gritter backgrounds */
.gritter-item-wrapper {
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#EA323232', endColorstr='#EA323232',GradientType=0 ) !important; /* IE6-9 */
}
.gritter-item-wrapper.gritter-info {
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#EA315185', endColorstr='#EA315185',GradientType=0 ) !important; /* IE6-9 */
}
.gritter-item-wrapper.gritter-error {
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#EA992812', endColorstr='#EA992812',GradientType=0 ) !important; /* IE6-9 */
}
.gritter-item-wrapper.gritter-success {
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#EA59834B', endColorstr='#EA59834B',GradientType=0 ) !important; /* IE6-9 */
}
.gritter-item-wrapper.gritter-warning {
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#EABE701F', endColorstr='#EABE701F',GradientType=0 ) !important; /* IE6-9 */
}

.gritter-item-wrapper.gritter-light {
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#F2F5F5F5', endColorstr='#F2F5F5F5',GradientType=0 ) !important; /* IE6-9 */
}
.gritter-info.gritter-light {
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#F2E8F2FF', endColorstr='#F2E8F2FF',GradientType=0 ) !important; /* IE6-9 */
}
.gritter-error.gritter-light {
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#F2FFEBEB', endColorstr='#F2FFEBEB',GradientType=0 ) !important; /* IE6-9 */
}
.gritter-success.gritter-light {
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#F2EFFAE3', endColorstr='#F2EFFAE3',GradientType=0 ) !important; /* IE6-9 */
}
.gritter-warning.gritter-light {
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#F2FCF8E3', endColorstr='#F2FCF8E3',GradientType=0 ) !important; /* IE6-9 */
}




.widget-header .wysiwyg-toolbar .btn-group > .btn , .widget-body .md-header .btn  {
  background:transparent none !important;
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#40FFFFFF', endColorstr='#40FFFFFF',GradientType=0 ) !important;
}
.widget-header .wysiwyg-toolbar .btn-group > .btn.active , .widget-body .md-header .btn-inverse {
  background:transparent none !important;
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#40000000', endColorstr='#40000000',GradientType=0 ) !important;
}
.widget-body .md-header .btn  , .widget-body .md-header .btn-inverse {
	display:none;
}

/*
//the plugin doesn't work in IE
.tag .close:hover {
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#33000000', endColorstr='#33000000',GradientType=0 ) !important;
}
*/



/* a few skinny changes */
body.skin-3, .skin-3 .sidebar {
 background-color:#D6D6D6;
}
.skin-3  .menu-min .nav-list > li.active > a {
 background-color:#EEF8FF;
}


body.skin-2 , .skin-2 .sidebar{
 background-color:#505050;
}
.skin-2  .nav-list > li:hover > a > span {
 color:#FFF;
}
.skin-2  .nav-list > li:hover > a > [class*="icon-"]:first-child {
 color:#FFF;
}
.skin-2  .menu-min .nav-list > li > a:hover > [class*="icon-"]:first-child {
 color:#FFF;
}



/* a few RTL changes */
.rtl .nav-list li.active > a:after {
 border-left-color: transparent;
 border-right-color: #0B6CBC;
}
.rtl.skin-1 .nav-list  li.active > a:after {
 border-right-color:#FFF;
 border-left-color:transparent;
}
.rtl.skin-1 .menu-min .nav-list  li.active:hover > a:after {
 border-right-color:#242A2B;
}
.rtl.skin-2 .nav-list  li.active > a:after {
 border-right-color:#FFF;
 border-left-color:transparent;
}
.rtl.skin-2 .menu-min .nav-list  li.active:hover > a:after {
 border-right-color:#292929;
 border-left-color:transparent;
}
.rtl.skin-2 .nav-list  li.active.open > .submenu > li.active > a:after {
 border-right-color:#FFF;
 border-left-color:transparent;
}
.rtl.skin-3 .nav-list li.active > a:after{
 border-right-color:#FFF;
 border-left-color:transparent;
}
.rtl.skin-3 .nav-list  li.active > a:before {
 border-right-color:#A4C6DD;
 border-left-color:transparent;
}
