/*! X-editable - v1.4.6 
* In-place editing with Twitter Bootstrap, jQuery UI or pure jQuery
* http://github.com/vitalets/x-editable
* Copyright (c) 2013 <PERSON><PERSON>y Potapov; Licensed MIT */
(function(b){var a=function(d,c){this.options=b.extend({},b.fn.editableform.defaults,c);this.$div=b(d);if(!this.options.scope){this.options.scope=this}};a.prototype={constructor:a,initInput:function(){this.input=this.options.input;this.value=this.input.str2value(this.options.value)},initTemplate:function(){this.$form=b(b.fn.editableform.template)},initButtons:function(){var c=this.$form.find(".editable-buttons");c.append(b.fn.editableform.buttons);if(this.options.showbuttons==="bottom"){c.addClass("editable-buttons-bottom")}},render:function(){this.$loading=b(b.fn.editableform.loading);this.$div.empty().append(this.$loading);this.initTemplate();if(this.options.showbuttons){this.initButtons()}else{this.$form.find(".editable-buttons").remove()}this.showLoading();this.isSaving=false;this.$div.triggerHandler("rendering");this.initInput();this.input.prerender();this.$form.find("div.editable-input").append(this.input.$tpl);this.$div.append(this.$form);b.when(this.input.render()).then(b.proxy(function(){if(!this.options.showbuttons){this.input.autosubmit()}this.$form.find(".editable-cancel").click(b.proxy(this.cancel,this));if(this.input.error){this.error(this.input.error);this.$form.find(".editable-submit").attr("disabled",true);this.input.$input.attr("disabled",true);this.$form.submit(function(d){d.preventDefault()})}else{this.error(false);this.input.$input.removeAttr("disabled");this.$form.find(".editable-submit").removeAttr("disabled");var c=(this.value===null||this.value===undefined||this.value==="")?this.options.defaultValue:this.value;this.input.value2input(c);this.$form.submit(b.proxy(this.submit,this))}this.$div.triggerHandler("rendered");this.showForm();if(this.input.postrender){this.input.postrender()}},this))},cancel:function(){this.$div.triggerHandler("cancel")},showLoading:function(){var c,d;if(this.$form){c=this.$form.outerWidth();d=this.$form.outerHeight();if(c){this.$loading.width(c)}if(d){this.$loading.height(d)}this.$form.hide()}else{c=this.$loading.parent().width();if(c){this.$loading.width(c)}}this.$loading.show()},showForm:function(c){this.$loading.hide();this.$form.show();if(c!==false){this.input.activate()}this.$div.triggerHandler("show")},error:function(g){var e=this.$form.find(".control-group"),f=this.$form.find(".editable-error-block"),c;if(g===false){e.removeClass(b.fn.editableform.errorGroupClass);f.removeClass(b.fn.editableform.errorBlockClass).empty().hide()}else{if(g){c=g.split("\n");for(var d=0;d<c.length;d++){c[d]=b("<div>").text(c[d]).html()}g=c.join("<br>")}e.addClass(b.fn.editableform.errorGroupClass);f.addClass(b.fn.editableform.errorBlockClass).html(g).show()}},submit:function(g){g.stopPropagation();g.preventDefault();var c,f=this.input.input2value();if(c=this.validate(f)){this.error(c);this.showForm();return}if(!this.options.savenochange&&this.input.value2str(f)==this.input.value2str(this.value)){this.$div.triggerHandler("nochange");return}var d=this.input.value2submit(f);this.isSaving=true;b.when(this.save(d)).done(b.proxy(function(e){this.isSaving=false;var h=typeof this.options.success==="function"?this.options.success.call(this.options.scope,e,f):null;if(h===false){this.error(false);this.showForm(false);return}if(typeof h==="string"){this.error(h);this.showForm();return}if(h&&typeof h==="object"&&h.hasOwnProperty("newValue")){f=h.newValue}this.error(false);this.value=f;this.$div.triggerHandler("save",{newValue:f,submitValue:d,response:e})},this)).fail(b.proxy(function(h){this.isSaving=false;var e;if(typeof this.options.error==="function"){e=this.options.error.call(this.options.scope,h,f)}else{e=typeof h==="string"?h:h.responseText||h.statusText||"Unknown error!"}this.error(e);this.showForm()},this))},save:function(d){this.options.pk=b.fn.editableutils.tryParseJson(this.options.pk,true);var c=(typeof this.options.pk==="function")?this.options.pk.call(this.options.scope):this.options.pk,e=!!(typeof this.options.url==="function"||(this.options.url&&((this.options.send==="always")||(this.options.send==="auto"&&c!==null&&c!==undefined)))),f;if(e){this.showLoading();f={name:this.options.name||"",value:d,pk:c};if(typeof this.options.params==="function"){f=this.options.params.call(this.options.scope,f)}else{this.options.params=b.fn.editableutils.tryParseJson(this.options.params,true);b.extend(f,this.options.params)}if(typeof this.options.url==="function"){return this.options.url.call(this.options.scope,f)}else{return b.ajax(b.extend({url:this.options.url,data:f,type:"POST"},this.options.ajaxOptions))}}},validate:function(c){if(c===undefined){c=this.value}if(typeof this.options.validate==="function"){return this.options.validate.call(this.options.scope,c)}},option:function(c,d){if(c in this.options){this.options[c]=d}if(c==="value"){this.setValue(d)}},setValue:function(c,d){if(d){this.value=this.input.str2value(c)}else{this.value=c}if(this.$form&&this.$form.is(":visible")){this.input.value2input(this.value)}}};b.fn.editableform=function(d){var c=arguments;return this.each(function(){var g=b(this),f=g.data("editableform"),e=typeof d==="object"&&d;if(!f){g.data("editableform",(f=new a(this,e)))}if(typeof d==="string"){f[d].apply(f,Array.prototype.slice.call(c,1))}})};b.fn.editableform.Constructor=a;b.fn.editableform.defaults={type:"text",url:null,params:null,name:null,pk:null,value:null,defaultValue:null,send:"auto",validate:null,success:null,error:null,ajaxOptions:null,showbuttons:true,scope:null,savenochange:false};b.fn.editableform.template='<form class="form-inline editableform"><div class="control-group"><div><div class="editable-input"></div><div class="editable-buttons"></div></div><div class="editable-error-block"></div></div></form>';b.fn.editableform.loading='<div class="editableform-loading"></div>';b.fn.editableform.buttons='<button type="submit" class="editable-submit">ok</button><button type="button" class="editable-cancel">cancel</button>';b.fn.editableform.errorGroupClass=null;b.fn.editableform.errorBlockClass="editable-error"}(window.jQuery));(function(a){a.fn.editableutils={inherit:function(c,b){var d=function(){};d.prototype=b.prototype;c.prototype=new d();c.prototype.constructor=c;c.superclass=b.prototype},setCursorPosition:function(c,d){if(c.setSelectionRange){c.setSelectionRange(d,d)}else{if(c.createTextRange){var b=c.createTextRange();b.collapse(true);b.moveEnd("character",d);b.moveStart("character",d);b.select()}}},tryParseJson:function(b,c){if(typeof b==="string"&&b.length&&b.match(/^[\{\[].*[\}\]]$/)){if(c){try{b=(new Function("return "+b))()}catch(d){}finally{return b}}else{b=(new Function("return "+b))()}}return b},sliceObj:function(h,g,b){var f,e,c={};if(!a.isArray(g)||!g.length){return c}for(var d=0;d<g.length;d++){f=g[d];if(h.hasOwnProperty(f)){c[f]=h[f]}if(b===true){continue}e=f.toLowerCase();if(h.hasOwnProperty(e)){c[f]=h[e]}}return c},getConfigData:function(b){var c={};a.each(b.data(),function(e,d){if(typeof d!=="object"||(d&&typeof d==="object"&&(d.constructor===Object||d.constructor===Array))){c[e]=d}});return c},objectKeys:function(d){if(Object.keys){return Object.keys(d)}else{if(d!==Object(d)){throw new TypeError("Object.keys called on a non-object")}var b=[],c;for(c in d){if(Object.prototype.hasOwnProperty.call(d,c)){b.push(c)}}return b}},escape:function(b){return a("<div>").text(b).html()},itemsByValue:function(h,g,e){if(!g||h===null){return[]}if(typeof(e)!=="function"){var c=e||"value";e=function(i){return i[c]}}var d=a.isArray(h),b=[],f=this;a.each(g,function(j,l){if(l.children){b=b.concat(f.itemsByValue(h,l.children,e))}else{if(d){if(a.grep(h,function(i){return i==(l&&typeof l==="object"?e(l):l)}).length){b.push(l)}}else{var k=(l&&(typeof l==="object"))?e(l):l;if(h==k){b.push(l)}}}});return b},createInput:function(c){var e,f,b,d=c.type;if(d==="date"){if(c.mode==="inline"){if(a.fn.editabletypes.datefield){d="datefield"}else{if(a.fn.editabletypes.dateuifield){d="dateuifield"}}}else{if(a.fn.editabletypes.date){d="date"}else{if(a.fn.editabletypes.dateui){d="dateui"}}}if(d==="date"&&!a.fn.editabletypes.date){d="combodate"}}if(d==="datetime"&&c.mode==="inline"){d="datetimefield"}if(d==="wysihtml5"&&!a.fn.editabletypes[d]){d="textarea"}if(typeof a.fn.editabletypes[d]==="function"){e=a.fn.editabletypes[d];f=this.sliceObj(c,this.objectKeys(e.defaults));b=new e(f);return b}else{a.error("Unknown type: "+d);return false}},supportsTransitions:function(){var c=document.body||document.documentElement,f=c.style,g="transition",d=["Moz","Webkit","Khtml","O","ms"];if(typeof f[g]==="string"){return true}g=g.charAt(0).toUpperCase()+g.substr(1);for(var e=0;e<d.length;e++){if(typeof f[d[e]+g]==="string"){return true}}return false}}}(window.jQuery));(function(c){var b=function(e,d){this.init(e,d)};var a=function(e,d){this.init(e,d)};b.prototype={containerName:null,containerDataName:null,innerCss:null,containerClass:"editable-container editable-popup",init:function(e,d){this.$element=c(e);this.options=c.extend({},c.fn.editableContainer.defaults,d);this.splitOptions();this.formOptions.scope=this.$element[0];this.initContainer();this.delayedHide=false;this.$element.on("destroyed",c.proxy(function(){this.destroy()},this));if(!c(document).data("editable-handlers-attached")){c(document).on("keyup.editable",function(f){if(f.which===27){c(".editable-open").editableContainer("hide")}});c(document).on("click.editable",function(j){var f=c(j.target),g,h=[".editable-container",".ui-datepicker-header",".datepicker",".modal-backdrop",".bootstrap-wysihtml5-insert-image-modal",".bootstrap-wysihtml5-insert-link-modal"];if(!c.contains(document.documentElement,j.target)){return}if(f.is(document)){return}for(g=0;g<h.length;g++){if(f.is(h[g])||f.parents(h[g]).length){return}}b.prototype.closeOthers(j.target)});c(document).data("editable-handlers-attached",true)}},splitOptions:function(){this.containerOptions={};this.formOptions={};if(!c.fn[this.containerName]){throw new Error(this.containerName+" not found. Have you included corresponding js file?")}var e=c.fn[this.containerName].defaults;for(var d in this.options){if(d in e){this.containerOptions[d]=this.options[d]}else{this.formOptions[d]=this.options[d]}}},tip:function(){return this.container()?this.container().$tip:null},container:function(){var d;if(this.containerDataName){if(d=this.$element.data(this.containerDataName)){return d}}d=this.$element.data(this.containerName);return d},call:function(){this.$element[this.containerName].apply(this.$element,arguments)},initContainer:function(){this.call(this.containerOptions)},renderForm:function(){this.$form.editableform(this.formOptions).on({save:c.proxy(this.save,this),nochange:c.proxy(function(){this.hide("nochange")},this),cancel:c.proxy(function(){this.hide("cancel")},this),show:c.proxy(function(){if(this.delayedHide){this.hide(this.delayedHide.reason);this.delayedHide=false}else{this.setPosition()}},this),rendering:c.proxy(this.setPosition,this),resize:c.proxy(this.setPosition,this),rendered:c.proxy(function(){this.$element.triggerHandler("shown",c(this.options.scope).data("editable"))},this)}).editableform("render")},show:function(d){this.$element.addClass("editable-open");if(d!==false){this.closeOthers(this.$element[0])}this.innerShow();this.tip().addClass(this.containerClass);if(this.$form){}this.$form=c("<div>");if(this.tip().is(this.innerCss)){this.tip().append(this.$form)}else{this.tip().find(this.innerCss).append(this.$form)}this.renderForm()},hide:function(d){if(!this.tip()||!this.tip().is(":visible")||!this.$element.hasClass("editable-open")){return}if(this.$form.data("editableform").isSaving){this.delayedHide={reason:d};return}else{this.delayedHide=false}this.$element.removeClass("editable-open");this.innerHide();this.$element.triggerHandler("hidden",d||"manual")},innerShow:function(){},innerHide:function(){},toggle:function(d){if(this.container()&&this.tip()&&this.tip().is(":visible")){this.hide()}else{this.show(d)}},setPosition:function(){},save:function(d,f){this.$element.triggerHandler("save",f);this.hide("save")},option:function(d,e){this.options[d]=e;if(d in this.containerOptions){this.containerOptions[d]=e;this.setContainerOption(d,e)}else{this.formOptions[d]=e;if(this.$form){this.$form.editableform("option",d,e)}}},setContainerOption:function(d,e){this.call("option",d,e)},destroy:function(){this.hide();this.innerDestroy();this.$element.off("destroyed");this.$element.removeData("editableContainer")},innerDestroy:function(){},closeOthers:function(d){c(".editable-open").each(function(g,h){if(h===d||c(h).find(d).length){return}var f=c(h),e=f.data("editableContainer");if(!e){return}if(e.options.onblur==="cancel"){f.data("editableContainer").hide("onblur")}else{if(e.options.onblur==="submit"){f.data("editableContainer").tip().find("form").submit()}}})},activate:function(){if(this.tip&&this.tip().is(":visible")&&this.$form){this.$form.data("editableform").input.activate()}}};c.fn.editableContainer=function(e){var d=arguments;return this.each(function(){var h=c(this),j="editableContainer",g=h.data(j),f=typeof e==="object"&&e,i=(f.mode==="inline")?a:b;if(!g){h.data(j,(g=new i(this,f)))}if(typeof e==="string"){g[e].apply(g,Array.prototype.slice.call(d,1))}})};c.fn.editableContainer.Popup=b;c.fn.editableContainer.Inline=a;c.fn.editableContainer.defaults={value:null,placement:"top",autohide:true,onblur:"cancel",anim:false,mode:"popup"};jQuery.event.special.destroyed={remove:function(d){if(d.handler){d.handler()}}}}(window.jQuery));(function(a){a.extend(a.fn.editableContainer.Inline.prototype,a.fn.editableContainer.Popup.prototype,{containerName:"editableform",innerCss:".editable-inline",containerClass:"editable-container editable-inline",initContainer:function(){this.$tip=a("<span></span>");if(!this.options.anim){this.options.anim=0}},splitOptions:function(){this.containerOptions={};this.formOptions=this.options},tip:function(){return this.$tip},innerShow:function(){this.$element.hide();this.tip().insertAfter(this.$element).show()},innerHide:function(){this.$tip.hide(this.options.anim,a.proxy(function(){this.$element.show();this.innerDestroy()},this))},innerDestroy:function(){if(this.tip()){this.tip().empty().remove()}}})}(window.jQuery));(function(b){var a=function(d,c){this.$element=b(d);this.options=b.extend({},b.fn.editable.defaults,c,b.fn.editableutils.getConfigData(this.$element));if(this.options.selector){this.initLive()}else{this.init()}if(this.options.highlight&&!b.fn.editableutils.supportsTransitions()){this.options.highlight=false}};a.prototype={constructor:a,init:function(){var c=false,d,e;this.options.name=this.options.name||this.$element.attr("id");this.options.scope=this.$element[0];this.input=b.fn.editableutils.createInput(this.options);if(!this.input){return}if(this.options.value===undefined||this.options.value===null){this.value=this.input.html2value(b.trim(this.$element.html()));c=true}else{this.options.value=b.fn.editableutils.tryParseJson(this.options.value,true);if(typeof this.options.value==="string"){this.value=this.input.str2value(this.options.value)}else{this.value=this.options.value}}this.$element.addClass("editable");if(this.input.type==="textarea"){this.$element.addClass("editable-pre-wrapped")}if(this.options.toggle!=="manual"){this.$element.addClass("editable-click");this.$element.on(this.options.toggle+".editable",b.proxy(function(g){if(!this.options.disabled){g.preventDefault()}if(this.options.toggle==="mouseenter"){this.show()}else{var f=(this.options.toggle!=="click");this.toggle(f)}},this))}else{this.$element.attr("tabindex",-1)}if(typeof this.options.display==="function"){this.options.autotext="always"}switch(this.options.autotext){case"always":d=true;break;case"auto":d=!b.trim(this.$element.text()).length&&this.value!==null&&this.value!==undefined&&!c;break;default:d=false}b.when(d?this.render():true).then(b.proxy(function(){if(this.options.disabled){this.disable()}else{this.enable()}this.$element.triggerHandler("init",this)},this))},initLive:function(){var c=this.options.selector;this.options.selector=false;this.options.autotext="never";this.$element.on(this.options.toggle+".editable",c,b.proxy(function(f){var d=b(f.target);if(!d.data("editable")){if(d.hasClass(this.options.emptyclass)){d.empty()}d.editable(this.options).trigger(f)}},this))},render:function(c){if(this.options.display===false){return}if(this.input.value2htmlFinal){return this.input.value2html(this.value,this.$element[0],this.options.display,c)}else{if(typeof this.options.display==="function"){return this.options.display.call(this.$element[0],this.value,c)}else{return this.input.value2html(this.value,this.$element[0])}}},enable:function(){this.options.disabled=false;this.$element.removeClass("editable-disabled");this.handleEmpty(this.isEmpty);if(this.options.toggle!=="manual"){if(this.$element.attr("tabindex")==="-1"){this.$element.removeAttr("tabindex")}}},disable:function(){this.options.disabled=true;this.hide();this.$element.addClass("editable-disabled");this.handleEmpty(this.isEmpty);this.$element.attr("tabindex",-1)},toggleDisabled:function(){if(this.options.disabled){this.enable()}else{this.disable()}},option:function(c,d){if(c&&typeof c==="object"){b.each(c,b.proxy(function(f,e){this.option(b.trim(f),e)},this));return}this.options[c]=d;if(c==="disabled"){return d?this.disable():this.enable()}if(c==="value"){this.setValue(d)}if(this.container){this.container.option(c,d)}if(this.input.option){this.input.option(c,d)}},handleEmpty:function(c){if(this.options.display===false){return}if(c!==undefined){this.isEmpty=c}else{if(b.trim(this.$element.html())===""){this.isEmpty=true}else{if(b.trim(this.$element.text())!==""){this.isEmpty=false}else{this.isEmpty=!this.$element.height()||!this.$element.width()}}}if(!this.options.disabled){if(this.isEmpty){this.$element.html(this.options.emptytext);if(this.options.emptyclass){this.$element.addClass(this.options.emptyclass)}}else{if(this.options.emptyclass){this.$element.removeClass(this.options.emptyclass)}}}else{if(this.isEmpty){this.$element.empty();if(this.options.emptyclass){this.$element.removeClass(this.options.emptyclass)}}}},show:function(c){if(this.options.disabled){return}if(!this.container){var d=b.extend({},this.options,{value:this.value,input:this.input});this.$element.editableContainer(d);this.$element.on("save.internal",b.proxy(this.save,this));this.container=this.$element.data("editableContainer")}else{if(this.container.tip().is(":visible")){return}}this.container.show(c)},hide:function(){if(this.container){this.container.hide()}},toggle:function(c){if(this.container&&this.container.tip().is(":visible")){this.hide()}else{this.show(c)}},save:function(g,h){if(this.options.unsavedclass){var d=false;d=d||typeof this.options.url==="function";d=d||this.options.display===false;d=d||h.response!==undefined;d=d||(this.options.savenochange&&this.input.value2str(this.value)!==this.input.value2str(h.newValue));if(d){this.$element.removeClass(this.options.unsavedclass)}else{this.$element.addClass(this.options.unsavedclass)}}if(this.options.highlight){var c=this.$element,f=c.css("background-color");c.css("background-color",this.options.highlight);setTimeout(function(){if(f==="transparent"){f=""}c.css("background-color",f);c.addClass("editable-bg-transition");setTimeout(function(){c.removeClass("editable-bg-transition")},1700)},10)}this.setValue(h.newValue,false,h.response)},validate:function(){if(typeof this.options.validate==="function"){return this.options.validate.call(this,this.value)}},setValue:function(d,e,c){if(e){this.value=this.input.str2value(d)}else{this.value=d}if(this.container){this.container.option("value",this.value)}b.when(this.render(c)).then(b.proxy(function(){this.handleEmpty()},this))},activate:function(){if(this.container){this.container.activate()}},destroy:function(){this.disable();if(this.container){this.container.destroy()}this.input.destroy();if(this.options.toggle!=="manual"){this.$element.removeClass("editable-click");this.$element.off(this.options.toggle+".editable")}this.$element.off("save.internal");this.$element.removeClass("editable editable-open editable-disabled");this.$element.removeData("editable")}};b.fn.editable=function(h){var c={},g=arguments,e="editable";switch(h){case"validate":this.each(function(){var m=b(this),l=m.data(e),k;if(l&&(k=l.validate())){c[l.options.name]=k}});return c;case"getValue":if(arguments.length===2&&arguments[1]===true){c=this.eq(0).data(e).value}else{this.each(function(){var l=b(this),k=l.data(e);if(k&&k.value!==undefined&&k.value!==null){c[k.options.name]=k.input.value2submit(k.value)}})}return c;case"submit":var f=arguments[1]||{},j=this,i=this.editable("validate"),d;if(b.isEmptyObject(i)){d=this.editable("getValue");if(f.data){b.extend(d,f.data)}b.ajax(b.extend({url:f.url,data:d,type:"POST"},f.ajaxOptions)).success(function(k){if(typeof f.success==="function"){f.success.call(j,k,f)}}).error(function(){if(typeof f.error==="function"){f.error.apply(j,arguments)}})}else{if(typeof f.error==="function"){f.error.call(j,i)}}return this}return this.each(function(){var m=b(this),l=m.data(e),k=typeof h==="object"&&h;if(k&&k.selector){l=new a(this,k);return}if(!l){m.data(e,(l=new a(this,k)))}if(typeof h==="string"){l[h].apply(l,Array.prototype.slice.call(g,1))}})};b.fn.editable.defaults={type:"text",disabled:false,toggle:"click",emptytext:"Empty",autotext:"auto",value:null,display:null,emptyclass:"editable-empty",unsavedclass:"editable-unsaved",selector:null,highlight:"#FFFF80"}}(window.jQuery));(function(b){b.fn.editabletypes={};var a=function(){};a.prototype={init:function(d,c,e){this.type=d;this.options=b.extend({},e,c)},prerender:function(){this.$tpl=b(this.options.tpl);this.$input=this.$tpl;this.$clear=null;this.error=null},render:function(){},value2html:function(d,c){b(c).text(b.trim(d))},html2value:function(c){return b("<div>").html(c).text()},value2str:function(c){return c},str2value:function(c){return c},value2submit:function(c){return c},value2input:function(c){this.$input.val(c)},input2value:function(){return this.$input.val()},activate:function(){if(this.$input.is(":visible")){this.$input.focus()}},clear:function(){this.$input.val(null)},escape:function(c){return b("<div>").text(c).html()},autosubmit:function(){},destroy:function(){},setClass:function(){if(this.options.inputclass){this.$input.addClass(this.options.inputclass)}},setAttr:function(c){if(this.options[c]!==undefined&&this.options[c]!==null){this.$input.attr(c,this.options[c])}},option:function(c,d){this.options[c]=d}};a.defaults={tpl:"",inputclass:"input-medium",scope:null,showbuttons:true};b.extend(b.fn.editabletypes,{abstractinput:a})}(window.jQuery));(function(b){var a=function(c){};b.fn.editableutils.inherit(a,b.fn.editabletypes.abstractinput);b.extend(a.prototype,{render:function(){var c=b.Deferred();this.error=null;this.onSourceReady(function(){this.renderList();c.resolve()},function(){this.error=this.options.sourceError;c.resolve()});return c.promise()},html2value:function(c){return null},value2html:function(f,e,g,d){var c=b.Deferred(),h=function(){if(typeof g==="function"){g.call(e,f,this.sourceData,d)}else{this.value2htmlFinal(f,e)}c.resolve()};if(f===null){h.call(this)}else{this.onSourceReady(h,function(){c.resolve()})}return c.promise()},onSourceReady:function(i,d){var f;if(b.isFunction(this.options.source)){f=this.options.source.call(this.options.scope);this.sourceData=null}else{f=this.options.source}if(this.options.sourceCache&&b.isArray(this.sourceData)){i.call(this);return}try{f=b.fn.editableutils.tryParseJson(f,false)}catch(h){d.call(this);return}if(typeof f==="string"){if(this.options.sourceCache){var g=f,c;if(!b(document).data(g)){b(document).data(g,{})}c=b(document).data(g);if(c.loading===false&&c.sourceData){this.sourceData=c.sourceData;this.doPrepend();i.call(this);return}else{if(c.loading===true){c.callbacks.push(b.proxy(function(){this.sourceData=c.sourceData;this.doPrepend();i.call(this)},this));c.err_callbacks.push(b.proxy(d,this));return}else{c.loading=true;c.callbacks=[];c.err_callbacks=[]}}}b.ajax({url:f,type:"get",cache:false,dataType:"json",success:b.proxy(function(e){if(c){c.loading=false}this.sourceData=this.makeArray(e);if(b.isArray(this.sourceData)){if(c){c.sourceData=this.sourceData;b.each(c.callbacks,function(){this.call()})}this.doPrepend();i.call(this)}else{d.call(this);if(c){b.each(c.err_callbacks,function(){this.call()})}}},this),error:b.proxy(function(){d.call(this);if(c){c.loading=false;b.each(c.err_callbacks,function(){this.call()})}},this)})}else{this.sourceData=this.makeArray(f);if(b.isArray(this.sourceData)){this.doPrepend();i.call(this)}else{d.call(this)}}},doPrepend:function(){if(this.options.prepend===null||this.options.prepend===undefined){return}if(!b.isArray(this.prependData)){if(b.isFunction(this.options.prepend)){this.options.prepend=this.options.prepend.call(this.options.scope)}this.options.prepend=b.fn.editableutils.tryParseJson(this.options.prepend,true);if(typeof this.options.prepend==="string"){this.options.prepend={"":this.options.prepend}}this.prependData=this.makeArray(this.options.prepend)}if(b.isArray(this.prependData)&&b.isArray(this.sourceData)){this.sourceData=this.prependData.concat(this.sourceData)}},renderList:function(){},value2htmlFinal:function(d,c){},makeArray:function(h){var g,j,c=[],f,d;if(!h||typeof h==="string"){return null}if(b.isArray(h)){d=function(l,i){j={value:l,text:i};if(g++>=2){return false}};for(var e=0;e<h.length;e++){f=h[e];if(typeof f==="object"){g=0;b.each(f,d);if(g===1){c.push(j)}else{if(g>1){if(f.children){f.children=this.makeArray(f.children)}c.push(f)}}}else{c.push({value:f,text:f})}}}else{b.each(h,function(l,i){c.push({value:l,text:i})})}return c},option:function(c,d){this.options[c]=d;if(c==="source"){this.sourceData=null}if(c==="prepend"){this.prependData=null}}});a.defaults=b.extend({},b.fn.editabletypes.abstractinput.defaults,{source:null,prepend:false,sourceError:"Error when loading list",sourceCache:true});b.fn.editabletypes.list=a}(window.jQuery));(function(b){var a=function(c){this.init("text",c,a.defaults)};b.fn.editableutils.inherit(a,b.fn.editabletypes.abstractinput);b.extend(a.prototype,{render:function(){this.renderClear();this.setClass();this.setAttr("placeholder")},activate:function(){if(this.$input.is(":visible")){this.$input.focus();b.fn.editableutils.setCursorPosition(this.$input.get(0),this.$input.val().length);if(this.toggleClear){this.toggleClear()}}},renderClear:function(){if(this.options.clear){this.$clear=b('<span class="editable-clear-x"></span>');this.$input.after(this.$clear).css("padding-right",24).keyup(b.proxy(function(d){if(~b.inArray(d.keyCode,[40,38,9,13,27])){return}clearTimeout(this.t);var c=this;this.t=setTimeout(function(){c.toggleClear(d)},100)},this)).parent().css("position","relative");this.$clear.click(b.proxy(this.clear,this))}},postrender:function(){},toggleClear:function(d){if(!this.$clear){return}var c=this.$input.val().length,f=this.$clear.is(":visible");if(c&&!f){this.$clear.show()}if(!c&&f){this.$clear.hide()}},clear:function(){this.$clear.hide();this.$input.val("").focus()}});a.defaults=b.extend({},b.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="text">',placeholder:null,clear:true});b.fn.editabletypes.text=a}(window.jQuery));(function(b){var a=function(c){this.init("textarea",c,a.defaults)};b.fn.editableutils.inherit(a,b.fn.editabletypes.abstractinput);b.extend(a.prototype,{render:function(){this.setClass();this.setAttr("placeholder");this.setAttr("rows");this.$input.keydown(function(c){if(c.ctrlKey&&c.which===13){b(this).closest("form").submit()}})},activate:function(){b.fn.editabletypes.text.prototype.activate.call(this)}});a.defaults=b.extend({},b.fn.editabletypes.abstractinput.defaults,{tpl:"<textarea></textarea>",inputclass:"input-large",placeholder:null,rows:7});b.fn.editabletypes.textarea=a}(window.jQuery));(function(a){var b=function(c){this.init("select",c,b.defaults)};a.fn.editableutils.inherit(b,a.fn.editabletypes.list);a.extend(b.prototype,{renderList:function(){this.$input.empty();var c=function(f,g){var d;if(a.isArray(g)){for(var e=0;e<g.length;e++){d={};if(g[e].children){d.label=g[e].text;f.append(c(a("<optgroup>",d),g[e].children))}else{d.value=g[e].value;if(g[e].disabled){d.disabled=true}f.append(a("<option>",d).text(g[e].text))}}}return f};c(this.$input,this.sourceData);this.setClass();this.$input.on("keydown.editable",function(d){if(d.which===13){a(this).closest("form").submit()}})},value2htmlFinal:function(e,d){var f="",c=a.fn.editableutils.itemsByValue(e,this.sourceData);if(c.length){f=c[0].text}a(d).text(f)},autosubmit:function(){this.$input.off("keydown.editable").on("change.editable",function(){a(this).closest("form").submit()})}});b.defaults=a.extend({},a.fn.editabletypes.list.defaults,{tpl:"<select></select>"});a.fn.editabletypes.select=b}(window.jQuery));(function(b){var a=function(c){this.init("checklist",c,a.defaults)};b.fn.editableutils.inherit(a,b.fn.editabletypes.list);b.extend(a.prototype,{renderList:function(){var c,d;this.$tpl.empty();if(!b.isArray(this.sourceData)){return}for(var e=0;e<this.sourceData.length;e++){c=b("<label>").append(b("<input>",{type:"checkbox",value:this.sourceData[e].value})).append(b("<span>").text(" "+this.sourceData[e].text));b("<div>").append(c).appendTo(this.$tpl)}this.$input=this.$tpl.find('input[type="checkbox"]');this.setClass()},value2str:function(c){return b.isArray(c)?c.sort().join(b.trim(this.options.separator)):""},str2value:function(e){var c,d=null;if(typeof e==="string"&&e.length){c=new RegExp("\\s*"+b.trim(this.options.separator)+"\\s*");d=e.split(c)}else{if(b.isArray(e)){d=e}else{d=[e]}}return d},value2input:function(c){this.$input.prop("checked",false);if(b.isArray(c)&&c.length){this.$input.each(function(e,f){var d=b(f);b.each(c,function(g,h){if(d.val()==h){d.prop("checked",true)}})})}},input2value:function(){var c=[];this.$input.filter(":checked").each(function(d,e){c.push(b(e).val())});return c},value2htmlFinal:function(f,d){var c=[],e=b.fn.editableutils.itemsByValue(f,this.sourceData);if(e.length){b.each(e,function(h,g){c.push(b.fn.editableutils.escape(g.text))});b(d).html(c.join("<br>"))}else{b(d).empty()}},activate:function(){this.$input.first().focus()},autosubmit:function(){this.$input.on("keydown",function(c){if(c.which===13){b(this).closest("form").submit()}})}});a.defaults=b.extend({},b.fn.editabletypes.list.defaults,{tpl:'<div class="editable-checklist"></div>',inputclass:null,separator:","});b.fn.editabletypes.checklist=a}(window.jQuery));(function(b){var a=function(c){this.init("password",c,a.defaults)};b.fn.editableutils.inherit(a,b.fn.editabletypes.text);b.extend(a.prototype,{value2html:function(d,c){if(d){b(c).text("[hidden]")}else{b(c).empty()}},html2value:function(c){return null}});a.defaults=b.extend({},b.fn.editabletypes.text.defaults,{tpl:'<input type="password">'});b.fn.editabletypes.password=a}(window.jQuery));(function(a){var b=function(c){this.init("email",c,b.defaults)};a.fn.editableutils.inherit(b,a.fn.editabletypes.text);b.defaults=a.extend({},a.fn.editabletypes.text.defaults,{tpl:'<input type="email">'});a.fn.editabletypes.email=b}(window.jQuery));(function(a){var b=function(c){this.init("url",c,b.defaults)};a.fn.editableutils.inherit(b,a.fn.editabletypes.text);b.defaults=a.extend({},a.fn.editabletypes.text.defaults,{tpl:'<input type="url">'});a.fn.editabletypes.url=b}(window.jQuery));(function(a){var b=function(c){this.init("tel",c,b.defaults)};a.fn.editableutils.inherit(b,a.fn.editabletypes.text);b.defaults=a.extend({},a.fn.editabletypes.text.defaults,{tpl:'<input type="tel">'});a.fn.editabletypes.tel=b}(window.jQuery));(function(a){var b=function(c){this.init("number",c,b.defaults)};a.fn.editableutils.inherit(b,a.fn.editabletypes.text);a.extend(b.prototype,{render:function(){b.superclass.render.call(this);this.setAttr("min");this.setAttr("max");this.setAttr("step")},postrender:function(){if(this.$clear){this.$clear.css({right:24})}}});b.defaults=a.extend({},a.fn.editabletypes.text.defaults,{tpl:'<input type="number">',inputclass:"input-mini",min:null,max:null,step:null});a.fn.editabletypes.number=b}(window.jQuery));(function(a){var b=function(c){this.init("range",c,b.defaults)};a.fn.editableutils.inherit(b,a.fn.editabletypes.number);a.extend(b.prototype,{render:function(){this.$input=this.$tpl.filter("input");this.setClass();this.setAttr("min");this.setAttr("max");this.setAttr("step");this.$input.on("input",function(){a(this).siblings("output").text(a(this).val())})},activate:function(){this.$input.focus()}});b.defaults=a.extend({},a.fn.editabletypes.number.defaults,{tpl:'<input type="range"><output style="width: 30px; display: inline-block"></output>',inputclass:"input-medium"});a.fn.editabletypes.range=b}(window.jQuery));(function(b){var a=function(c){this.init("time",c,a.defaults)};b.fn.editableutils.inherit(a,b.fn.editabletypes.abstractinput);b.extend(a.prototype,{render:function(){this.setClass()}});a.defaults=b.extend({},b.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="time">'});b.fn.editabletypes.time=a}(window.jQuery));(function(a){var b=function(d){this.init("select2",d,b.defaults);d.select2=d.select2||{};this.sourceData=null;if(d.placeholder){d.select2.placeholder=d.placeholder}if(!d.select2.tags&&d.source){var e=d.source;if(a.isFunction(d.source)){e=d.source.call(d.scope)}if(typeof e==="string"){d.select2.ajax=d.select2.ajax||{};if(!d.select2.ajax.data){d.select2.ajax.data=function(f){return{query:f}}}if(!d.select2.ajax.results){d.select2.ajax.results=function(f){return{results:f}}}d.select2.ajax.url=e}else{this.sourceData=this.convertSource(e);d.select2.data=this.sourceData}}this.options.select2=a.extend({},b.defaults.select2,d.select2);this.isMultiple=this.options.select2.tags||this.options.select2.multiple;this.isRemote=("ajax" in this.options.select2);this.idFunc=this.options.select2.id;if(typeof(this.idFunc)!=="function"){var c=this.idFunc||"id";this.idFunc=function(f){return f[c]}}this.formatSelection=this.options.select2.formatSelection;if(typeof(this.formatSelection)!=="function"){this.formatSelection=function(f){return f.text}}};a.fn.editableutils.inherit(b,a.fn.editabletypes.abstractinput);a.extend(b.prototype,{render:function(){this.setClass();this.$input.select2(this.options.select2);if(this.isRemote){this.$input.on("select2-loaded",a.proxy(function(c){this.sourceData=c.items.results},this))}if(this.isMultiple){this.$input.on("change",function(){a(this).closest("form").parent().triggerHandler("resize")})}},value2html:function(f,c){var g="",e,d=this;if(this.options.select2.tags){e=f}else{if(this.sourceData){e=a.fn.editableutils.itemsByValue(f,this.sourceData,this.idFunc)}else{}}if(a.isArray(e)){g=[];a.each(e,function(i,h){g.push(h&&typeof h==="object"?d.formatSelection(h):h)})}else{if(e){g=d.formatSelection(e)}}g=a.isArray(g)?g.join(this.options.viewseparator):g;a(c).text(g)},html2value:function(c){return this.options.select2.tags?this.str2value(c,this.options.viewseparator):null},value2input:function(e){this.$input.val(e).trigger("change",true);if(this.isRemote&&!this.isMultiple&&!this.options.select2.initSelection){var f=this.options.select2.id,c=this.options.select2.formatSelection;if(!f&&!c){var d={id:e,text:a(this.options.scope).text()};this.$input.select2("data",d)}}},input2value:function(){return this.$input.select2("val")},str2value:function(g,e){if(typeof g!=="string"||!this.isMultiple){return g}e=e||this.options.select2.separator||a.fn.select2.defaults.separator;var f,d,c;if(g===null||g.length<1){return null}f=g.split(e);for(d=0,c=f.length;d<c;d=d+1){f[d]=a.trim(f[d])}return f},autosubmit:function(){this.$input.on("change",function(d,c){if(!c){a(this).closest("form").submit()}})},convertSource:function(d){if(a.isArray(d)&&d.length&&d[0].value!==undefined){for(var c=0;c<d.length;c++){if(d[c].value!==undefined){d[c].id=d[c].value;delete d[c].value}}}return d},destroy:function(){if(this.$input.data("select2")){this.$input.select2("destroy")}}});b.defaults=a.extend({},a.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="hidden">',select2:null,placeholder:null,source:null,viewseparator:", "});a.fn.editabletypes.select2=b}(window.jQuery));(function(b){var a=function(d,c){this.$element=b(d);if(!this.$element.is("input")){b.error("Combodate should be applied to INPUT element");return}this.options=b.extend({},b.fn.combodate.defaults,c,this.$element.data());this.init()};a.prototype={constructor:a,init:function(){this.map={day:["D","date"],month:["M","month"],year:["Y","year"],hour:["[Hh]","hours"],minute:["m","minutes"],second:["s","seconds"],ampm:["[Aa]",""]};this.$widget=b('<span class="combodate"></span>').html(this.getTemplate());this.initCombos();this.$widget.on("change","select",b.proxy(function(){this.$element.val(this.getValue())},this));this.$widget.find("select").css("width","auto");this.$element.hide().after(this.$widget);this.setValue(this.$element.val()||this.options.value)},getTemplate:function(){var c=this.options.template;b.each(this.map,function(e,d){d=d[0];var g=new RegExp(d+"+"),f=d.length>1?d.substring(1,2):d;c=c.replace(g,"{"+f+"}")});c=c.replace(/ /g,"&nbsp;");b.each(this.map,function(e,d){d=d[0];var f=d.length>1?d.substring(1,2):d;c=c.replace("{"+f+"}",'<select class="'+e+'"></select>')});return c},initCombos:function(){var c=this;b.each(this.map,function(g,e){var h=c.$widget.find("."+g),i,d;if(h.length){c["$"+g]=h;i="fill"+g.charAt(0).toUpperCase()+g.slice(1);d=c[i]();c["$"+g].html(c.renderItems(d))}})},initItems:function(d){var c=[],e;if(this.options.firstItem==="name"){e=moment.relativeTime||moment.langData()._relativeTime;var f=typeof e[d]==="function"?e[d](1,true,d,false):e[d];f=f.split(" ").reverse()[0];c.push(["",f])}else{if(this.options.firstItem==="empty"){c.push(["",""])}}return c},renderItems:function(c){var e=[];for(var d=0;d<c.length;d++){e.push('<option value="'+c[d][0]+'">'+c[d][1]+"</option>")}return e.join("\n")},fillDay:function(){var d=this.initItems("d"),e,f,c=this.options.template.indexOf("DD")!==-1;for(f=1;f<=31;f++){e=c?this.leadZero(f):f;d.push([f,e])}return d},fillMonth:function(){var d=this.initItems("M"),e,f,h=this.options.template.indexOf("MMMM")!==-1,g=this.options.template.indexOf("MMM")!==-1,c=this.options.template.indexOf("MM")!==-1;for(f=0;f<=11;f++){if(h){e=moment().date(1).month(f).format("MMMM")}else{if(g){e=moment().date(1).month(f).format("MMM")}else{if(c){e=this.leadZero(f+1)}else{e=f+1}}}d.push([f,e])}return d},fillYear:function(){var c=[],d,e,f=this.options.template.indexOf("YYYY")!==-1;for(e=this.options.maxYear;e>=this.options.minYear;e--){d=f?e:(e+"").substring(2);c[this.options.yearDescending?"push":"unshift"]([e,d])}c=this.initItems("y").concat(c);return c},fillHour:function(){var e=this.initItems("h"),f,h,k=this.options.template.indexOf("h")!==-1,j=this.options.template.indexOf("H")!==-1,d=this.options.template.toLowerCase().indexOf("hh")!==-1,g=k?1:0,c=k?12:23;for(h=g;h<=c;h++){f=d?this.leadZero(h):h;e.push([h,f])}return e},fillMinute:function(){var d=this.initItems("m"),e,f,c=this.options.template.indexOf("mm")!==-1;for(f=0;f<=59;f+=this.options.minuteStep){e=c?this.leadZero(f):f;d.push([f,e])}return d},fillSecond:function(){var d=this.initItems("s"),e,f,c=this.options.template.indexOf("ss")!==-1;for(f=0;f<=59;f+=this.options.secondStep){e=c?this.leadZero(f):f;d.push([f,e])}return d},fillAmpm:function(){var d=this.options.template.indexOf("a")!==-1,e=this.options.template.indexOf("A")!==-1,c=[["am",d?"am":"AM"],["pm",d?"pm":"PM"]];return c},getValue:function(f){var e,c={},d=this,g=false;b.each(this.map,function(i,h){if(i==="ampm"){return}var j=i==="day"?1:0;c[i]=d["$"+i]?parseInt(d["$"+i].val(),10):j;if(isNaN(c[i])){g=true;return false}});if(g){return""}if(this.$ampm){if(c.hour===12){c.hour=this.$ampm.val()==="am"?0:12}else{c.hour=this.$ampm.val()==="am"?c.hour:c.hour+12}}e=moment([c.year,c.month,c.day,c.hour,c.minute,c.second]);this.highlight(e);f=f===undefined?this.options.format:f;if(f===null){return e.isValid()?e:null}else{return e.isValid()?e.format(f):""}},setValue:function(f){if(!f){return}var e=typeof f==="string"?moment(f,this.options.format):moment(f),d=this,c={};function g(h,i){var j={};h.children("option").each(function(l,k){var m=b(k).attr("value"),n;if(m===""){return}n=Math.abs(m-i);if(typeof j.distance==="undefined"||n<j.distance){j={value:m,distance:n}}});return j.value}if(e.isValid()){b.each(this.map,function(i,h){if(i==="ampm"){return}c[i]=e[h[1]]()});if(this.$ampm){if(c.hour>=12){c.ampm="pm";if(c.hour>12){c.hour-=12}}else{c.ampm="am";if(c.hour===0){c.hour=12}}}b.each(c,function(i,h){if(d["$"+i]){if(i==="minute"&&d.options.minuteStep>1&&d.options.roundTime){h=g(d["$"+i],h)}if(i==="second"&&d.options.secondStep>1&&d.options.roundTime){h=g(d["$"+i],h)}d["$"+i].val(h)}});this.$element.val(e.format(this.options.format))}},highlight:function(c){if(!c.isValid()){if(this.options.errorClass){this.$widget.addClass(this.options.errorClass)}else{if(!this.borderColor){this.borderColor=this.$widget.find("select").css("border-color")}this.$widget.find("select").css("border-color","red")}}else{if(this.options.errorClass){this.$widget.removeClass(this.options.errorClass)}else{this.$widget.find("select").css("border-color",this.borderColor)}}},leadZero:function(c){return c<=9?"0"+c:c},destroy:function(){this.$widget.remove();this.$element.removeData("combodate").show()}};b.fn.combodate=function(e){var f,c=Array.apply(null,arguments);c.shift();if(e==="getValue"&&this.length&&(f=this.eq(0).data("combodate"))){return f.getValue.apply(f,c)}return this.each(function(){var h=b(this),g=h.data("combodate"),d=typeof e=="object"&&e;if(!g){h.data("combodate",(g=new a(this,d)))}if(typeof e=="string"&&typeof g[e]=="function"){g[e].apply(g,c)}})};b.fn.combodate.defaults={format:"DD-MM-YYYY HH:mm",template:"D / MMM / YYYY   H : mm",value:null,minYear:1970,maxYear:2015,yearDescending:true,minuteStep:5,secondStep:1,firstItem:"empty",errorClass:null,roundTime:true}}(window.jQuery));(function(a){var b=function(c){this.init("combodate",c,b.defaults);if(!this.options.viewformat){this.options.viewformat=this.options.format}c.combodate=a.fn.editableutils.tryParseJson(c.combodate,true);this.options.combodate=a.extend({},b.defaults.combodate,c.combodate,{format:this.options.format,template:this.options.template})};a.fn.editableutils.inherit(b,a.fn.editabletypes.abstractinput);a.extend(b.prototype,{render:function(){this.$input.combodate(this.options.combodate)},value2html:function(d,c){var e=d?d.format(this.options.viewformat):"";a(c).text(e)},html2value:function(c){return c?moment(c,this.options.viewformat):null},value2str:function(c){return c?c.format(this.options.format):""},str2value:function(c){return c?moment(c,this.options.format):null},value2submit:function(c){return this.value2str(c)},value2input:function(c){this.$input.combodate("setValue",c)},input2value:function(){return this.$input.combodate("getValue",null)},activate:function(){this.$input.siblings(".combodate").find("select").eq(0).focus()},autosubmit:function(){}});b.defaults=a.extend({},a.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="text">',inputclass:null,format:"YYYY-MM-DD",viewformat:null,template:"D / MMM / YYYY",combodate:null});a.fn.editabletypes.combodate=b}(window.jQuery));(function(a){a.extend(a.fn.editableform.Constructor.prototype,{initTemplate:function(){this.$form=a(a.fn.editableform.template);this.$form.find(".editable-error-block").addClass("help-block")}});a.fn.editableform.buttons='<button type="submit" class="btn btn-primary editable-submit"><i class="icon-ok icon-white"></i></button><button type="button" class="btn editable-cancel"><i class="icon-remove"></i></button>';a.fn.editableform.errorGroupClass="error";a.fn.editableform.errorBlockClass=null}(window.jQuery));(function(a){a.extend(a.fn.editableContainer.Popup.prototype,{containerName:"popover",innerCss:a.fn.popover&&a(a.fn.popover.Constructor.DEFAULTS.template).find("p").length?".popover-content p":".popover-content",initContainer:function(){a.extend(this.containerOptions,{trigger:"manual",selector:false,content:" ",template:a.fn.popover.Constructor.DEFAULTS.template});var b;if(this.$element.data("template")){b=this.$element.data("template");this.$element.removeData("template")}this.call(this.containerOptions);if(b){this.$element.data("template",b)}},innerShow:function(){this.call("show")},innerHide:function(){this.call("hide")},innerDestroy:function(){this.call("destroy")},setContainerOption:function(b,c){this.container().options[b]=c},setPosition:function(){(function(){var g=this.tip(),e,j,b,h,f,k,c,l,i,d;f=typeof this.options.placement==="function"?this.options.placement.call(this,g[0],this.$element[0]):this.options.placement;e=/in/.test(f);g.removeClass("top right bottom left").css({top:0,left:0,display:"block"});j=this.getPosition(e);b=g[0].offsetWidth;h=g[0].offsetHeight;f=e?f.split(" ")[1]:f;l={top:j.top+j.height,left:j.left+j.width/2-b/2};c={top:j.top-h,left:j.left+j.width/2-b/2};i={top:j.top+j.height/2-h/2,left:j.left-b};d={top:j.top+j.height/2-h/2,left:j.left+j.width};switch(f){case"bottom":if((l.top+h)>(a(window).scrollTop()+a(window).height())){if(c.top>a(window).scrollTop()){f="top"}else{if((d.left+b)<(a(window).scrollLeft()+a(window).width())){f="right"}else{if(i.left>a(window).scrollLeft()){f="left"}else{f="right"}}}}break;case"top":if(c.top<a(window).scrollTop()){if((l.top+h)<(a(window).scrollTop()+a(window).height())){f="bottom"}else{if((d.left+b)<(a(window).scrollLeft()+a(window).width())){f="right"}else{if(i.left>a(window).scrollLeft()){f="left"}else{f="right"}}}}break;case"left":if(i.left<a(window).scrollLeft()){if((d.left+b)<(a(window).scrollLeft()+a(window).width())){f="right"}else{if(c.top>a(window).scrollTop()){f="top"}else{if(c.top>a(window).scrollTop()){f="bottom"}else{f="right"}}}}break;case"right":if((d.left+b)>(a(window).scrollLeft()+a(window).width())){if(i.left>a(window).scrollLeft()){f="left"}else{if(c.top>a(window).scrollTop()){f="top"}else{if(c.top>a(window).scrollTop()){f="bottom"}}}}break}switch(f){case"bottom":k=l;break;case"top":k=c;break;case"left":k=i;break;case"right":k=d;break}g.offset(k).addClass(f).addClass("in")}).call(this.container())}})}(window.jQuery));(function(g){function k(){return new Date(Date.UTC.apply(Date,arguments))}function d(){var n=new Date();return k(n.getUTCFullYear(),n.getUTCMonth(),n.getUTCDate())}var h=function(o,n){var p=this;this._process_options(n);this.element=g(o);this.isInline=false;this.isInput=this.element.is("input");this.component=this.element.is(".date")?this.element.find(".add-on, .btn"):false;this.hasInput=this.component&&this.element.find("input").length;if(this.component&&this.component.length===0){this.component=false}this.picker=g(i.template);this._buildEvents();this._attachEvents();if(this.isInline){this.picker.addClass("datepicker-inline").appendTo(this.element)}else{this.picker.addClass("datepicker-dropdown dropdown-menu")}if(this.o.rtl){this.picker.addClass("datepicker-rtl");this.picker.find(".prev i, .next i").toggleClass("icon-arrow-left icon-arrow-right")}this.viewMode=this.o.startView;if(this.o.calendarWeeks){this.picker.find("tfoot th.today").attr("colspan",function(q,r){return parseInt(r)+1})}this._allow_update=false;this.setStartDate(this.o.startDate);this.setEndDate(this.o.endDate);this.setDaysOfWeekDisabled(this.o.daysOfWeekDisabled);this.fillDow();this.fillMonths();this._allow_update=true;this.update();this.showMode();if(this.isInline){this.show()}};h.prototype={constructor:h,_process_options:function(n){this._o=g.extend({},this._o,n);var r=this.o=g.extend({},this._o);var q=r.language;if(!b[q]){q=q.split("-")[0];if(!b[q]){q=e.language}}r.language=q;switch(r.startView){case 2:case"decade":r.startView=2;break;case 1:case"year":r.startView=1;break;default:r.startView=0}switch(r.minViewMode){case 1:case"months":r.minViewMode=1;break;case 2:case"years":r.minViewMode=2;break;default:r.minViewMode=0}r.startView=Math.max(r.startView,r.minViewMode);r.weekStart%=7;r.weekEnd=((r.weekStart+6)%7);var p=i.parseFormat(r.format);if(r.startDate!==-Infinity){r.startDate=i.parseDate(r.startDate,p,r.language)}if(r.endDate!==Infinity){r.endDate=i.parseDate(r.endDate,p,r.language)}r.daysOfWeekDisabled=r.daysOfWeekDisabled||[];if(!g.isArray(r.daysOfWeekDisabled)){r.daysOfWeekDisabled=r.daysOfWeekDisabled.split(/[,\s]*/)}r.daysOfWeekDisabled=g.map(r.daysOfWeekDisabled,function(o){return parseInt(o,10)})},_events:[],_secondaryEvents:[],_applyEvents:function(n){for(var o=0,p,q;o<n.length;o++){p=n[o][0];q=n[o][1];p.on(q)}},_unapplyEvents:function(n){for(var o=0,p,q;o<n.length;o++){p=n[o][0];q=n[o][1];p.off(q)}},_buildEvents:function(){if(this.isInput){this._events=[[this.element,{focus:g.proxy(this.show,this),keyup:g.proxy(this.update,this),keydown:g.proxy(this.keydown,this)}]]}else{if(this.component&&this.hasInput){this._events=[[this.element.find("input"),{focus:g.proxy(this.show,this),keyup:g.proxy(this.update,this),keydown:g.proxy(this.keydown,this)}],[this.component,{click:g.proxy(this.show,this)}]]}else{if(this.element.is("div")){this.isInline=true}else{this._events=[[this.element,{click:g.proxy(this.show,this)}]]}}}this._secondaryEvents=[[this.picker,{click:g.proxy(this.click,this)}],[g(window),{resize:g.proxy(this.place,this)}],[g(document),{mousedown:g.proxy(function(n){if(!(this.element.is(n.target)||this.element.find(n.target).size()||this.picker.is(n.target)||this.picker.find(n.target).size())){this.hide()}},this)}]]},_attachEvents:function(){this._detachEvents();this._applyEvents(this._events)},_detachEvents:function(){this._unapplyEvents(this._events)},_attachSecondaryEvents:function(){this._detachSecondaryEvents();this._applyEvents(this._secondaryEvents)},_detachSecondaryEvents:function(){this._unapplyEvents(this._secondaryEvents)},_trigger:function(p,q){var o=q||this.date,n=new Date(o.getTime()+(o.getTimezoneOffset()*60000));this.element.trigger({type:p,date:n,format:g.proxy(function(s){var r=s||this.o.format;return i.formatDate(o,r,this.o.language)},this)})},show:function(n){if(!this.isInline){this.picker.appendTo("body")}this.picker.show();this.height=this.component?this.component.outerHeight():this.element.outerHeight();this.place();this._attachSecondaryEvents();if(n){n.preventDefault()}this._trigger("show")},hide:function(n){if(this.isInline){return}if(!this.picker.is(":visible")){return}this.picker.hide().detach();this._detachSecondaryEvents();this.viewMode=this.o.startView;this.showMode();if(this.o.forceParse&&(this.isInput&&this.element.val()||this.hasInput&&this.element.find("input").val())){this.setValue()}this._trigger("hide")},remove:function(){this.hide();this._detachEvents();this._detachSecondaryEvents();this.picker.remove();delete this.element.data().datepicker;if(!this.isInput){delete this.element.data().date}},getDate:function(){var n=this.getUTCDate();return new Date(n.getTime()+(n.getTimezoneOffset()*60000))},getUTCDate:function(){return this.date},setDate:function(n){this.setUTCDate(new Date(n.getTime()-(n.getTimezoneOffset()*60000)))},setUTCDate:function(n){this.date=n;this.setValue()},setValue:function(){var n=this.getFormattedDate();if(!this.isInput){if(this.component){this.element.find("input").val(n)}}else{this.element.val(n)}},getFormattedDate:function(n){if(n===undefined){n=this.o.format}return i.formatDate(this.date,n,this.o.language)},setStartDate:function(n){this._process_options({startDate:n});this.update();this.updateNavArrows()},setEndDate:function(n){this._process_options({endDate:n});this.update();this.updateNavArrows()},setDaysOfWeekDisabled:function(n){this._process_options({daysOfWeekDisabled:n});this.update();this.updateNavArrows()},place:function(){if(this.isInline){return}var p=parseInt(this.element.parents().filter(function(){return g(this).css("z-index")!="auto"}).first().css("z-index"))+10;var o=this.component?this.component.parent().offset():this.element.offset();var n=this.component?this.component.outerHeight(true):this.element.outerHeight(true);this.picker.css({top:o.top+n,left:o.left,zIndex:p})},_allow_update:true,update:function(){if(!this._allow_update){return}var n,o=false;if(arguments&&arguments.length&&(typeof arguments[0]==="string"||arguments[0] instanceof Date)){n=arguments[0];o=true}else{n=this.isInput?this.element.val():this.element.data("date")||this.element.find("input").val();delete this.element.data().date}this.date=i.parseDate(n,this.o.format,this.o.language);if(o){this.setValue()}if(this.date<this.o.startDate){this.viewDate=new Date(this.o.startDate)}else{if(this.date>this.o.endDate){this.viewDate=new Date(this.o.endDate)}else{this.viewDate=new Date(this.date)}}this.fill()},fillDow:function(){var o=this.o.weekStart,p="<tr>";if(this.o.calendarWeeks){var n='<th class="cw">&nbsp;</th>';p+=n;this.picker.find(".datepicker-days thead tr:first-child").prepend(n)}while(o<this.o.weekStart+7){p+='<th class="dow">'+b[this.o.language].daysMin[(o++)%7]+"</th>"}p+="</tr>";this.picker.find(".datepicker-days thead").append(p)},fillMonths:function(){var o="",n=0;while(n<12){o+='<span class="month">'+b[this.o.language].monthsShort[n++]+"</span>"}this.picker.find(".datepicker-months td").html(o)},setRange:function(n){if(!n||!n.length){delete this.range}else{this.range=g.map(n,function(o){return o.valueOf()})}this.fill()},getClassNames:function(q){var o=[],r=this.viewDate.getUTCFullYear(),s=this.viewDate.getUTCMonth(),n=this.date.valueOf(),p=new Date();if(q.getUTCFullYear()<r||(q.getUTCFullYear()==r&&q.getUTCMonth()<s)){o.push("old")}else{if(q.getUTCFullYear()>r||(q.getUTCFullYear()==r&&q.getUTCMonth()>s)){o.push("new")}}if(this.o.todayHighlight&&q.getUTCFullYear()==p.getFullYear()&&q.getUTCMonth()==p.getMonth()&&q.getUTCDate()==p.getDate()){o.push("today")}if(n&&q.valueOf()==n){o.push("active")}if(q.valueOf()<this.o.startDate||q.valueOf()>this.o.endDate||g.inArray(q.getUTCDay(),this.o.daysOfWeekDisabled)!==-1){o.push("disabled")}if(this.range){if(q>this.range[0]&&q<this.range[this.range.length-1]){o.push("range")}if(g.inArray(q.valueOf(),this.range)!=-1){o.push("selected")}}return o},fill:function(){var F=new Date(this.viewDate),w=F.getUTCFullYear(),G=F.getUTCMonth(),A=this.o.startDate!==-Infinity?this.o.startDate.getUTCFullYear():-Infinity,E=this.o.startDate!==-Infinity?this.o.startDate.getUTCMonth():-Infinity,t=this.o.endDate!==Infinity?this.o.endDate.getUTCFullYear():Infinity,B=this.o.endDate!==Infinity?this.o.endDate.getUTCMonth():Infinity,u=this.date&&this.date.valueOf(),q;this.picker.find(".datepicker-days thead th.datepicker-switch").text(b[this.o.language].months[G]+" "+w);this.picker.find("tfoot th.today").text(b[this.o.language].today).toggle(this.o.todayBtn!==false);this.picker.find("tfoot th.clear").text(b[this.o.language].clear).toggle(this.o.clearBtn!==false);this.updateNavArrows();this.fillMonths();var I=k(w,G-1,28,0,0,0,0),D=i.getDaysInMonth(I.getUTCFullYear(),I.getUTCMonth());I.setUTCDate(D);I.setUTCDate(D-(I.getUTCDay()-this.o.weekStart+7)%7);var n=new Date(I);n.setUTCDate(n.getUTCDate()+42);n=n.valueOf();var v=[];var z;while(I.valueOf()<n){if(I.getUTCDay()==this.o.weekStart){v.push("<tr>");if(this.o.calendarWeeks){var o=new Date(+I+(this.o.weekStart-I.getUTCDay()-7)%7*86400000),r=new Date(+o+(7+4-o.getUTCDay())%7*86400000),p=new Date(+(p=k(r.getUTCFullYear(),0,1))+(7+4-p.getUTCDay())%7*86400000),x=(r-p)/86400000/7+1;v.push('<td class="cw">'+x+"</td>")}}z=this.getClassNames(I);z.push("day");var y=this.o.beforeShowDay(I);if(y===undefined){y={}}else{if(typeof(y)==="boolean"){y={enabled:y}}else{if(typeof(y)==="string"){y={classes:y}}}}if(y.enabled===false){z.push("disabled")}if(y.classes){z=z.concat(y.classes.split(/\s+/))}if(y.tooltip){q=y.tooltip}z=g.unique(z);v.push('<td class="'+z.join(" ")+'"'+(q?' title="'+q+'"':"")+">"+I.getUTCDate()+"</td>");if(I.getUTCDay()==this.o.weekEnd){v.push("</tr>")}I.setUTCDate(I.getUTCDate()+1)}this.picker.find(".datepicker-days tbody").empty().append(v.join(""));var J=this.date&&this.date.getUTCFullYear();var s=this.picker.find(".datepicker-months").find("th:eq(1)").text(w).end().find("span").removeClass("active");if(J&&J==w){s.eq(this.date.getUTCMonth()).addClass("active")}if(w<A||w>t){s.addClass("disabled")}if(w==A){s.slice(0,E).addClass("disabled")}if(w==t){s.slice(B+1).addClass("disabled")}v="";w=parseInt(w/10,10)*10;var H=this.picker.find(".datepicker-years").find("th:eq(1)").text(w+"-"+(w+9)).end().find("td");w-=1;for(var C=-1;C<11;C++){v+='<span class="year'+(C==-1?" old":C==10?" new":"")+(J==w?" active":"")+(w<A||w>t?" disabled":"")+'">'+w+"</span>";w+=1}H.html(v)},updateNavArrows:function(){if(!this._allow_update){return}var p=new Date(this.viewDate),n=p.getUTCFullYear(),o=p.getUTCMonth();switch(this.viewMode){case 0:if(this.o.startDate!==-Infinity&&n<=this.o.startDate.getUTCFullYear()&&o<=this.o.startDate.getUTCMonth()){this.picker.find(".prev").css({visibility:"hidden"})}else{this.picker.find(".prev").css({visibility:"visible"})}if(this.o.endDate!==Infinity&&n>=this.o.endDate.getUTCFullYear()&&o>=this.o.endDate.getUTCMonth()){this.picker.find(".next").css({visibility:"hidden"})}else{this.picker.find(".next").css({visibility:"visible"})}break;case 1:case 2:if(this.o.startDate!==-Infinity&&n<=this.o.startDate.getUTCFullYear()){this.picker.find(".prev").css({visibility:"hidden"})}else{this.picker.find(".prev").css({visibility:"visible"})}if(this.o.endDate!==Infinity&&n>=this.o.endDate.getUTCFullYear()){this.picker.find(".next").css({visibility:"hidden"})}else{this.picker.find(".next").css({visibility:"visible"})}break}},click:function(r){r.preventDefault();var s=g(r.target).closest("span, td, th");if(s.length==1){switch(s[0].nodeName.toLowerCase()){case"th":switch(s[0].className){case"datepicker-switch":this.showMode(1);break;case"prev":case"next":var n=i.modes[this.viewMode].navStep*(s[0].className=="prev"?-1:1);switch(this.viewMode){case 0:this.viewDate=this.moveMonth(this.viewDate,n);break;case 1:case 2:this.viewDate=this.moveYear(this.viewDate,n);break}this.fill();break;case"today":var o=new Date();o=k(o.getFullYear(),o.getMonth(),o.getDate(),0,0,0);this.showMode(-2);var p=this.o.todayBtn=="linked"?null:"view";this._setDate(o,p);break;case"clear":var q;if(this.isInput){q=this.element}else{if(this.component){q=this.element.find("input")}}if(q){q.val("").change()}this._trigger("changeDate");this.update();if(this.o.autoclose){this.hide()}break}break;case"span":if(!s.is(".disabled")){this.viewDate.setUTCDate(1);if(s.is(".month")){var v=1;var t=s.parent().find("span").index(s);var u=this.viewDate.getUTCFullYear();this.viewDate.setUTCMonth(t);this._trigger("changeMonth",this.viewDate);if(this.o.minViewMode===1){this._setDate(k(u,t,v,0,0,0,0))}}else{var u=parseInt(s.text(),10)||0;var v=1;var t=0;this.viewDate.setUTCFullYear(u);this._trigger("changeYear",this.viewDate);if(this.o.minViewMode===2){this._setDate(k(u,t,v,0,0,0,0))}}this.showMode(-1);this.fill()}break;case"td":if(s.is(".day")&&!s.is(".disabled")){var v=parseInt(s.text(),10)||1;var u=this.viewDate.getUTCFullYear(),t=this.viewDate.getUTCMonth();if(s.is(".old")){if(t===0){t=11;u-=1}else{t-=1}}else{if(s.is(".new")){if(t==11){t=0;u+=1}else{t+=1}}}this._setDate(k(u,t,v,0,0,0,0))}break}}},_setDate:function(n,p){if(!p||p=="date"){this.date=new Date(n)}if(!p||p=="view"){this.viewDate=new Date(n)}this.fill();this.setValue();this._trigger("changeDate");var o;if(this.isInput){o=this.element}else{if(this.component){o=this.element.find("input")}}if(o){o.change();if(this.o.autoclose&&(!p||p=="date")){this.hide()}}},moveMonth:function(n,o){if(!o){return n}var r=new Date(n.valueOf()),v=r.getUTCDate(),s=r.getUTCMonth(),q=Math.abs(o),u,t;o=o>0?1:-1;if(q==1){t=o==-1?function(){return r.getUTCMonth()==s}:function(){return r.getUTCMonth()!=u};u=s+o;r.setUTCMonth(u);if(u<0||u>11){u=(u+12)%12}}else{for(var p=0;p<q;p++){r=this.moveMonth(r,o)}u=r.getUTCMonth();r.setUTCDate(v);t=function(){return u!=r.getUTCMonth()}}while(t()){r.setUTCDate(--v);r.setUTCMonth(u)}return r},moveYear:function(o,n){return this.moveMonth(o,n*12)},dateWithinRange:function(n){return n>=this.o.startDate&&n<=this.o.endDate},keydown:function(u){if(this.picker.is(":not(:visible)")){if(u.keyCode==27){this.show()}return}var q=false,p,o,t,n,s;switch(u.keyCode){case 27:this.hide();u.preventDefault();break;case 37:case 39:if(!this.o.keyboardNavigation){break}p=u.keyCode==37?-1:1;if(u.ctrlKey){n=this.moveYear(this.date,p);s=this.moveYear(this.viewDate,p)}else{if(u.shiftKey){n=this.moveMonth(this.date,p);s=this.moveMonth(this.viewDate,p)}else{n=new Date(this.date);n.setUTCDate(this.date.getUTCDate()+p);s=new Date(this.viewDate);s.setUTCDate(this.viewDate.getUTCDate()+p)}}if(this.dateWithinRange(n)){this.date=n;this.viewDate=s;this.setValue();this.update();u.preventDefault();q=true}break;case 38:case 40:if(!this.o.keyboardNavigation){break}p=u.keyCode==38?-1:1;if(u.ctrlKey){n=this.moveYear(this.date,p);s=this.moveYear(this.viewDate,p)}else{if(u.shiftKey){n=this.moveMonth(this.date,p);s=this.moveMonth(this.viewDate,p)}else{n=new Date(this.date);n.setUTCDate(this.date.getUTCDate()+p*7);s=new Date(this.viewDate);s.setUTCDate(this.viewDate.getUTCDate()+p*7)}}if(this.dateWithinRange(n)){this.date=n;this.viewDate=s;this.setValue();this.update();u.preventDefault();q=true}break;case 13:this.hide();u.preventDefault();break;case 9:this.hide();break}if(q){this._trigger("changeDate");var r;if(this.isInput){r=this.element}else{if(this.component){r=this.element.find("input")}}if(r){r.change()}}},showMode:function(n){if(n){this.viewMode=Math.max(this.o.minViewMode,Math.min(2,this.viewMode+n))}this.picker.find(">div").hide().filter(".datepicker-"+i.modes[this.viewMode].clsName).css("display","block");this.updateNavArrows()}};var m=function(o,n){this.element=g(o);this.inputs=g.map(n.inputs,function(p){return p.jquery?p[0]:p});delete n.inputs;g(this.inputs).datepicker(n).bind("changeDate",g.proxy(this.dateUpdated,this));this.pickers=g.map(this.inputs,function(p){return g(p).data("datepicker")});this.updateDates()};m.prototype={updateDates:function(){this.dates=g.map(this.pickers,function(n){return n.date});this.updateRanges()},updateRanges:function(){var n=g.map(this.dates,function(o){return o.valueOf()});g.each(this.pickers,function(o,q){q.setRange(n)})},dateUpdated:function(q){var r=g(q.target).data("datepicker"),p=r.getUTCDate(),o=g.inArray(q.target,this.inputs),n=this.inputs.length;if(o==-1){return}if(p<this.dates[o]){while(o>=0&&p<this.dates[o]){this.pickers[o--].setUTCDate(p)}}else{if(p>this.dates[o]){while(o<n&&p>this.dates[o]){this.pickers[o++].setUTCDate(p)}}}this.updateDates()},remove:function(){g.map(this.pickers,function(n){n.remove()});delete this.element.data().datepicker}};function f(q,t){var s=g(q).data(),n={},r,p=new RegExp("^"+t.toLowerCase()+"([A-Z])"),t=new RegExp("^"+t.toLowerCase());for(var o in s){if(t.test(o)){r=o.replace(p,function(v,u){return u.toLowerCase()});n[r]=s[o]}}return n}function a(p){var n={};if(!b[p]){p=p.split("-")[0];if(!b[p]){return}}var o=b[p];g.each(l,function(r,q){if(q in o){n[q]=o[q]}});return n}var c=g.fn.datepicker;var j=g.fn.datepicker=function(q){var o=Array.apply(null,arguments);o.shift();var p,n;this.each(function(){var y=g(this),w=y.data("datepicker"),s=typeof q=="object"&&q;if(!w){var u=f(this,"date"),r=g.extend({},e,u,s),t=a(r.language),v=g.extend({},e,t,u,s);if(y.is(".input-daterange")||v.inputs){var x={inputs:v.inputs||y.find("input").toArray()};y.data("datepicker",(w=new m(this,g.extend(v,x))))}else{y.data("datepicker",(w=new h(this,v)))}}if(typeof q=="string"&&typeof w[q]=="function"){p=w[q].apply(w,o);if(p!==undefined){return false}}});if(p!==undefined){return p}else{return this}};var e=g.fn.datepicker.defaults={autoclose:false,beforeShowDay:g.noop,calendarWeeks:false,clearBtn:false,daysOfWeekDisabled:[],endDate:Infinity,forceParse:true,format:"mm/dd/yyyy",keyboardNavigation:true,language:"en",minViewMode:0,rtl:false,startDate:-Infinity,startView:0,todayBtn:false,todayHighlight:false,weekStart:0};var l=g.fn.datepicker.locale_opts=["format","rtl","weekStart"];g.fn.datepicker.Constructor=h;var b=g.fn.datepicker.dates={en:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat","Sun"],daysMin:["Su","Mo","Tu","We","Th","Fr","Sa","Su"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],today:"Today",clear:"Clear"}};var i={modes:[{clsName:"days",navFnc:"Month",navStep:1},{clsName:"months",navFnc:"FullYear",navStep:1},{clsName:"years",navFnc:"FullYear",navStep:10}],isLeapYear:function(n){return(((n%4===0)&&(n%100!==0))||(n%400===0))},getDaysInMonth:function(n,o){return[31,(i.isLeapYear(n)?29:28),31,30,31,30,31,31,30,31,30,31][o]},validParts:/dd?|DD?|mm?|MM?|yy(?:yy)?/g,nonpunctuation:/[^ -\/:-@\[\u3400-\u9fff-`{-~\t\n\r]+/g,parseFormat:function(p){var n=p.replace(this.validParts,"\0").split("\0"),o=p.match(this.validParts);if(!n||!n.length||!o||o.length===0){throw new Error("Invalid date format.")}return{separators:n,parts:o}},parseDate:function(q,A,u){if(q instanceof Date){return q}if(typeof A==="string"){A=i.parseFormat(A)}if(/^[\-+]\d+[dmwy]([\s,]+[\-+]\d+[dmwy])*$/.test(q)){var C=/([\-+]\d+)([dmwy])/,t=q.match(/([\-+]\d+)([dmwy])/g),n,r;q=new Date();for(var v=0;v<t.length;v++){n=C.exec(t[v]);r=parseInt(n[1]);switch(n[2]){case"d":q.setUTCDate(q.getUTCDate()+r);break;case"m":q=h.prototype.moveMonth.call(h.prototype,q,r);break;case"w":q.setUTCDate(q.getUTCDate()+r*7);break;case"y":q=h.prototype.moveYear.call(h.prototype,q,r);break}}return k(q.getUTCFullYear(),q.getUTCMonth(),q.getUTCDate(),0,0,0)}var t=q&&q.match(this.nonpunctuation)||[],q=new Date(),y={},z=["yyyy","yy","M","MM","m","mm","d","dd"],B={yyyy:function(E,s){return E.setUTCFullYear(s)},yy:function(E,s){return E.setUTCFullYear(2000+s)},m:function(E,s){s-=1;while(s<0){s+=12}s%=12;E.setUTCMonth(s);while(E.getUTCMonth()!=s){E.setUTCDate(E.getUTCDate()-1)}return E},d:function(E,s){return E.setUTCDate(s)}},p,w,n;B.M=B.MM=B.mm=B.m;B.dd=B.d;q=k(q.getFullYear(),q.getMonth(),q.getDate(),0,0,0);var x=A.parts.slice();if(t.length!=x.length){x=g(x).filter(function(s,E){return g.inArray(E,z)!==-1}).toArray()}if(t.length==x.length){for(var v=0,o=x.length;v<o;v++){p=parseInt(t[v],10);n=x[v];if(isNaN(p)){switch(n){case"MM":w=g(b[u].months).filter(function(){var s=this.slice(0,t[v].length),E=t[v].slice(0,s.length);return s==E});p=g.inArray(w[0],b[u].months)+1;break;case"M":w=g(b[u].monthsShort).filter(function(){var s=this.slice(0,t[v].length),E=t[v].slice(0,s.length);return s==E});p=g.inArray(w[0],b[u].monthsShort)+1;break}}y[n]=p}for(var v=0,D;v<z.length;v++){D=z[v];if(D in y&&!isNaN(y[D])){B[D](q,y[D])}}}return q},formatDate:function(n,r,t){if(typeof r==="string"){r=i.parseFormat(r)}var s={d:n.getUTCDate(),D:b[t].daysShort[n.getUTCDay()],DD:b[t].days[n.getUTCDay()],m:n.getUTCMonth()+1,M:b[t].monthsShort[n.getUTCMonth()],MM:b[t].months[n.getUTCMonth()],yy:n.getUTCFullYear().toString().substring(2),yyyy:n.getUTCFullYear()};s.dd=(s.d<10?"0":"")+s.d;s.mm=(s.m<10?"0":"")+s.m;var n=[],q=g.extend([],r.separators);for(var p=0,o=r.parts.length;p<=o;p++){if(q.length){n.push(q.shift())}n.push(s[r.parts[p]])}return n.join("")},headTemplate:'<thead><tr><th class="prev"><i class="icon-arrow-left"/></th><th colspan="5" class="datepicker-switch"></th><th class="next"><i class="icon-arrow-right"/></th></tr></thead>',contTemplate:'<tbody><tr><td colspan="7"></td></tr></tbody>',footTemplate:'<tfoot><tr><th colspan="7" class="today"></th></tr><tr><th colspan="7" class="clear"></th></tr></tfoot>'};i.template='<div class="datepicker"><div class="datepicker-days"><table class=" table-condensed">'+i.headTemplate+"<tbody></tbody>"+i.footTemplate+'</table></div><div class="datepicker-months"><table class="table-condensed">'+i.headTemplate+i.contTemplate+i.footTemplate+'</table></div><div class="datepicker-years"><table class="table-condensed">'+i.headTemplate+i.contTemplate+i.footTemplate+"</table></div></div>";g.fn.datepicker.DPGlobal=i;g.fn.datepicker.noConflict=function(){g.fn.datepicker=c;return this};g(document).on("focus.datepicker.data-api click.datepicker.data-api",'[data-provide="datepicker"]',function(o){var n=g(this);if(n.data("datepicker")){return}o.preventDefault();j.call(n,"show")});g(function(){j.call(g('[data-provide="datepicker-inline"]'))})}(window.jQuery));(function(b){b.fn.bdatepicker=b.fn.datepicker.noConflict();if(!b.fn.datepicker){b.fn.datepicker=b.fn.bdatepicker}var a=function(c){this.init("date",c,a.defaults);this.initPicker(c,a.defaults)};b.fn.editableutils.inherit(a,b.fn.editabletypes.abstractinput);b.extend(a.prototype,{initPicker:function(c,d){if(!this.options.viewformat){this.options.viewformat=this.options.format}c.datepicker=b.fn.editableutils.tryParseJson(c.datepicker,true);this.options.datepicker=b.extend({},d.datepicker,c.datepicker,{format:this.options.viewformat});this.options.datepicker.language=this.options.datepicker.language||"en";this.dpg=b.fn.bdatepicker.DPGlobal;this.parsedFormat=this.dpg.parseFormat(this.options.format);this.parsedViewFormat=this.dpg.parseFormat(this.options.viewformat)},render:function(){this.$input.bdatepicker(this.options.datepicker);if(this.options.clear){this.$clear=b('<a href="#"></a>').html(this.options.clear).click(b.proxy(function(c){c.preventDefault();c.stopPropagation();this.clear()},this));this.$tpl.parent().append(b('<div class="editable-clear">').append(this.$clear))}},value2html:function(d,c){var e=d?this.dpg.formatDate(d,this.parsedViewFormat,this.options.datepicker.language):"";a.superclass.value2html(e,c)},html2value:function(c){return this.parseDate(c,this.parsedViewFormat)},value2str:function(c){return c?this.dpg.formatDate(c,this.parsedFormat,this.options.datepicker.language):""},str2value:function(c){return this.parseDate(c,this.parsedFormat)},value2submit:function(c){return this.value2str(c)},value2input:function(c){this.$input.bdatepicker("update",c)},input2value:function(){return this.$input.data("datepicker").date},activate:function(){},clear:function(){this.$input.data("datepicker").date=null;this.$input.find(".active").removeClass("active");if(!this.options.showbuttons){this.$input.closest("form").submit()}},autosubmit:function(){this.$input.on("mouseup",".day",function(d){if(b(d.currentTarget).is(".old")||b(d.currentTarget).is(".new")){return}var c=b(this).closest("form");setTimeout(function(){c.submit()},200)})},parseDate:function(f,d){var c=null,e;if(f){c=this.dpg.parseDate(f,d,this.options.datepicker.language);if(typeof f==="string"){e=this.dpg.formatDate(c,d,this.options.datepicker.language);if(f!==e){c=null}}}return c}});a.defaults=b.extend({},b.fn.editabletypes.abstractinput.defaults,{tpl:'<div class="editable-date well"></div>',inputclass:null,format:"yyyy-mm-dd",viewformat:null,datepicker:{weekStart:0,startView:0,minViewMode:0,autoclose:false},clear:"&times; clear"});b.fn.editabletypes.date=a}(window.jQuery));(function(b){var a=function(c){this.init("datefield",c,a.defaults);this.initPicker(c,a.defaults)};b.fn.editableutils.inherit(a,b.fn.editabletypes.date);b.extend(a.prototype,{render:function(){this.$input=this.$tpl.find("input");this.setClass();this.setAttr("placeholder");this.$tpl.bdatepicker(this.options.datepicker);this.$input.off("focus keydown");this.$input.keyup(b.proxy(function(){this.$tpl.removeData("date");this.$tpl.bdatepicker("update")},this))},value2input:function(c){this.$input.val(c?this.dpg.formatDate(c,this.parsedViewFormat,this.options.datepicker.language):"");this.$tpl.bdatepicker("update")},input2value:function(){return this.html2value(this.$input.val())},activate:function(){b.fn.editabletypes.text.prototype.activate.call(this)},autosubmit:function(){}});a.defaults=b.extend({},b.fn.editabletypes.date.defaults,{tpl:'<div class="input-group input-append date"><input type="text"/><span class="add-on input-group-addon"><i class="icon-th"></i></span></div>',inputclass:"input-small",datepicker:{weekStart:0,startView:0,minViewMode:0,autoclose:true}});b.fn.editabletypes.datefield=a}(window.jQuery));(function(a){var b=function(c){this.init("datetime",c,b.defaults);this.initPicker(c,b.defaults)};a.fn.editableutils.inherit(b,a.fn.editabletypes.abstractinput);a.extend(b.prototype,{initPicker:function(c,d){if(!this.options.viewformat){this.options.viewformat=this.options.format}c.datetimepicker=a.fn.editableutils.tryParseJson(c.datetimepicker,true);this.options.datetimepicker=a.extend({},d.datetimepicker,c.datetimepicker,{format:this.options.viewformat});this.options.datetimepicker.language=this.options.datetimepicker.language||"en";this.dpg=a.fn.datetimepicker.DPGlobal;this.parsedFormat=this.dpg.parseFormat(this.options.format,this.options.formatType);this.parsedViewFormat=this.dpg.parseFormat(this.options.viewformat,this.options.formatType)},render:function(){this.$input.datetimepicker(this.options.datetimepicker);this.$input.on("changeMode",function(d){var c=a(this).closest("form").parent();setTimeout(function(){c.triggerHandler("resize")},0)});if(this.options.clear){this.$clear=a('<a href="#"></a>').html(this.options.clear).click(a.proxy(function(c){c.preventDefault();c.stopPropagation();this.clear()},this));this.$tpl.parent().append(a('<div class="editable-clear">').append(this.$clear))}},value2html:function(d,c){var e=d?this.dpg.formatDate(this.toUTC(d),this.parsedViewFormat,this.options.datetimepicker.language,this.options.formatType):"";if(c){b.superclass.value2html(e,c)}else{return e}},html2value:function(c){var d=this.parseDate(c,this.parsedViewFormat);return d?this.fromUTC(d):null},value2str:function(c){return c?this.dpg.formatDate(this.toUTC(c),this.parsedFormat,this.options.datetimepicker.language,this.options.formatType):""},str2value:function(d){var c=this.parseDate(d,this.parsedFormat);return c?this.fromUTC(c):null},value2submit:function(c){return this.value2str(c)},value2input:function(c){if(c){this.$input.data("datetimepicker").setDate(c)}},input2value:function(){var c=this.$input.data("datetimepicker");return c.date?c.getDate():null},activate:function(){},clear:function(){this.$input.data("datetimepicker").date=null;this.$input.find(".active").removeClass("active");if(!this.options.showbuttons){this.$input.closest("form").submit()}},autosubmit:function(){this.$input.on("mouseup",".minute",function(d){var c=a(this).closest("form");setTimeout(function(){c.submit()},200)})},toUTC:function(c){return c?new Date(c.valueOf()-c.getTimezoneOffset()*60000):c},fromUTC:function(c){return c?new Date(c.valueOf()+c.getTimezoneOffset()*60000):c},parseDate:function(f,d){var c=null,e;if(f){c=this.dpg.parseDate(f,d,this.options.datetimepicker.language,this.options.formatType);if(typeof f==="string"){e=this.dpg.formatDate(c,d,this.options.datetimepicker.language,this.options.formatType);if(f!==e){c=null}}}return c}});b.defaults=a.extend({},a.fn.editabletypes.abstractinput.defaults,{tpl:'<div class="editable-date well"></div>',inputclass:null,format:"yyyy-mm-dd hh:ii",formatType:"standard",viewformat:null,datetimepicker:{todayHighlight:false,autoclose:false},clear:"&times; clear"});a.fn.editabletypes.datetime=b}(window.jQuery));(function(b){var a=function(c){this.init("datetimefield",c,a.defaults);this.initPicker(c,a.defaults)};b.fn.editableutils.inherit(a,b.fn.editabletypes.datetime);b.extend(a.prototype,{render:function(){this.$input=this.$tpl.find("input");this.setClass();this.setAttr("placeholder");this.$tpl.datetimepicker(this.options.datetimepicker);this.$input.off("focus keydown");this.$input.keyup(b.proxy(function(){this.$tpl.removeData("date");this.$tpl.datetimepicker("update")},this))},value2input:function(c){this.$input.val(this.value2html(c));this.$tpl.datetimepicker("update")},input2value:function(){return this.html2value(this.$input.val())},activate:function(){b.fn.editabletypes.text.prototype.activate.call(this)},autosubmit:function(){}});a.defaults=b.extend({},b.fn.editabletypes.datetime.defaults,{tpl:'<div class="input-group input-append date"><input type="text"/><span class="input-group-addon add-on"><i class="icon-th"></i></span></div>',inputclass:"input-medium",datetimepicker:{todayHighlight:false,autoclose:true}});b.fn.editabletypes.datetimefield=a}(window.jQuery));(function(a){var b=function(c){this.init("typeahead",c,b.defaults);this.options.typeahead=a.extend({},b.defaults.typeahead,{matcher:this.matcher,sorter:this.sorter,highlighter:this.highlighter,updater:this.updater},c.typeahead)};a.fn.editableutils.inherit(b,a.fn.editabletypes.list);a.extend(b.prototype,{renderList:function(){this.$input=this.$tpl.is("input")?this.$tpl:this.$tpl.find('input[type="text"]');this.options.typeahead.source=this.sourceData;this.$input.typeahead(this.options.typeahead);var c=this.$input.data("typeahead");c.render=a.proxy(this.typeaheadRender,c);c.select=a.proxy(this.typeaheadSelect,c);c.move=a.proxy(this.typeaheadMove,c);this.renderClear();this.setClass();this.setAttr("placeholder")},value2htmlFinal:function(e,d){if(this.getIsObjects()){var c=a.fn.editableutils.itemsByValue(e,this.sourceData);a(d).text(c.length?c[0].text:"")}else{a(d).text(e)}},html2value:function(c){return c?c:null},value2input:function(d){if(this.getIsObjects()){var c=a.fn.editableutils.itemsByValue(d,this.sourceData);this.$input.data("value",d).val(c.length?c[0].text:"")}else{this.$input.val(d)}},input2value:function(){if(this.getIsObjects()){var d=this.$input.data("value"),c=a.fn.editableutils.itemsByValue(d,this.sourceData);if(c.length&&c[0].text.toLowerCase()===this.$input.val().toLowerCase()){return d}else{return null}}else{return this.$input.val()}},getIsObjects:function(){if(this.isObjects===undefined){this.isObjects=false;for(var c=0;c<this.sourceData.length;c++){if(this.sourceData[c].value!==this.sourceData[c].text){this.isObjects=true;break}}}return this.isObjects},activate:a.fn.editabletypes.text.prototype.activate,renderClear:a.fn.editabletypes.text.prototype.renderClear,postrender:a.fn.editabletypes.text.prototype.postrender,toggleClear:a.fn.editabletypes.text.prototype.toggleClear,clear:function(){a.fn.editabletypes.text.prototype.clear.call(this);this.$input.data("value","")},matcher:function(c){return a.fn.typeahead.Constructor.prototype.matcher.call(this,c.text)},sorter:function(e){var f=[],d=[],c=[],g,h;while(g=e.shift()){h=g.text;if(!h.toLowerCase().indexOf(this.query.toLowerCase())){f.push(g)}else{if(~h.indexOf(this.query)){d.push(g)}else{c.push(g)}}}return f.concat(d,c)},highlighter:function(c){return a.fn.typeahead.Constructor.prototype.highlighter.call(this,c.text)},updater:function(c){this.$element.data("value",c.value);return c.text},typeaheadRender:function(c){var d=this;c=a(c).map(function(e,f){e=a(d.options.item).data("item",f);e.find("a").html(d.highlighter(f));return e[0]});if(this.options.autoSelect){c.first().addClass("active")}this.$menu.html(c);return this},typeaheadSelect:function(){var c=this.$menu.find(".active").data("item");if(this.options.autoSelect||c){this.$element.val(this.updater(c)).change()}return this.hide()},typeaheadMove:function(c){if(!this.shown){return}switch(c.keyCode){case 9:case 13:case 27:if(!this.$menu.find(".active").length){return}c.preventDefault();break;case 38:c.preventDefault();this.prev();break;case 40:c.preventDefault();this.next();break}c.stopPropagation()}});b.defaults=a.extend({},a.fn.editabletypes.list.defaults,{tpl:'<input type="text">',typeahead:null,clear:true});a.fn.editabletypes.typeahead=b}(window.jQuery));