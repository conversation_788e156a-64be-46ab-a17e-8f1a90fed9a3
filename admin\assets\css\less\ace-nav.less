.navbar {
  margin-bottom:0;
}

.navbar {
  padding-left:0; padding-right:0;
  margin-left:0; margin-right:0;

 //.navbar-inner {
	  border:none;
	  .box-shadow(none);
	  .border-radius(0);

	  margin:0;
	  padding-left:0; padding-right:0;
	  
	  min-height:@navbar-mh;
	  position:relative;

	  background:@navbar-bg;
 // }

  > .container {
	padding-left:0;
	padding-right:0;
  }

  .navbar-text, .navbar-link {
	 color:@navbar-text-color;
  }
  .navbar-brand {
	 color:@navbar-text-color;
	 font-size:@brand-size;
	 text-shadow:none;
	 padding-top: 10px;
	 padding-bottom: 10px;
	 &:hover , &:focus {
		color: @navbar-text-color;
	 }
  }
  
  .navbar-header {
	margin:0 !important;
  }


  .navbar-nav {
	> li {
	   > a {
			& , &:hover , &:focus {
				font-size:13px;
				text-shadow:none;
				color:@navbar-text-color;
			}
		}
	}//li
  }//.nav

}
.navbar-fixed-top, .navbar-fixed-bottom {
	position: fixed;
}

.navbar-container {
	padding-left:10px;
	padding-right:20px;
}
.navbar-container.container {
	padding-left:0;
	padding-right:0;
}



/* ace-nav */
.ace-nav {
 height:100%;
 margin:0 !important;
 
 > li {
	line-height:@navbar-mh;
	height:@navbar-mh;

	border-left:1px solid #DDD;
	padding:0;

	position:relative;
	float:left !important;
	
	&:first-child {
	  border-left:none;
	}
	
	> a {
		background-color: @ace-nav-default;
		color: #FFF;

		display: block;
		line-height: inherit;
		text-align: center;

		height: 100%;
		width: auto;
		min-width: 50px;
		padding: 0 8px;

		position: relative;

		> [class*="icon-"] {
			font-size: 16px;
			color: #FFF;
			display: inline-block;
			width: 20px;
			text-align: center;
		}

		> .badge {
			position: relative;
			top: -4px;
			left: 2px;

			padding-right: 5px;
			padding-left: 5px;
		}
	}


	> a:hover , > a:focus {
		background-color: desaturate(darken(@ace-nav-default, 4%), 4%);
	}
	&.open > a {
		background-color: desaturate(darken(@ace-nav-default, 4%), 4%) !important;
	}

	/* different colors */
	.ace-nav-color(@color) {
	  @ace-nav-class: ~`"ace-nav-@{color}"`;
	  @ace-nav-bg: ~`"ace-nav-@{color}"`;
	  @ace-nav-cl: @@ace-nav-bg;

	  &.@{color} > a {
		background-color:@ace-nav-cl;
		&:hover , &:focus {
			background-color: desaturate(darken(@ace-nav-cl, 4%), 4%);
		}
	  }
	  &.open.@{color} > a {
		background-color: desaturate(darken(@ace-nav-cl, 4%), 4%) !important;
	  }
	}
	 &.open > a {
		color:#FFF !important;
	 }
	.ace-nav-color(~"grey");
	.ace-nav-color(~"purple");
	.ace-nav-color(~"green");
	.ace-nav-color(~"light-blue");
	.ace-nav-color(~"light-blue2");
	.ace-nav-color(~"red");
	.ace-nav-color(~"light-green");
	.ace-nav-color(~"light-purple");
	.ace-nav-color(~"light-orange");
	.ace-nav-color(~"light-pink");
	.ace-nav-color(~"dark");
	.ace-nav-color(~"white-opaque");
	.ace-nav-color(~"dark-opaque");



	
	//margins
	.marginX (@index) when (@index > 0) {
		&.margin-@{index} { margin-left: unit(@index,px); }
		.marginX(@index - 1);
	}
	.marginX(4);
	
	&.no-border {
		border: none !important;
	}

	
	///
	.dropdown-menu {
		z-index: @zindex-navbar-fixed + 1;
	}
 }
 
 .nav-user-photo {
	 margin: -4px 8px 0 0;

	 border-radius: 100%;
	 border: 2px solid #FFF;

	 max-width: 40px;
 }
 
 
  li:last-child  a [class^="icon-"] {/* the arrow indicating "See more" on each dropdown , and the icons of user menu */
	display: inline-block;
	width: 1.25em;
	text-align: center;
  }

}




/* ace-nav responsive */
@media only screen and (max-width: @screen-xs-max) {
 .navbar-container {
	padding-left:0;
	padding-right:0;
 }
}
@media only screen and (max-width: @screen-xs-max) {
 .ace-nav > li:nth-last-child(4) > .dropdown-menu.pull-right {
	right:-80px;
 }
 .ace-nav > li:nth-last-child(4) > .dropdown-menu.pull-right:before,
 .ace-nav > li:nth-last-child(4) > .dropdown-menu.pull-right:after {
  right:100px;
 }
 
 .ace-nav > li:nth-last-child(3) > .dropdown-menu.pull-right {
	right:-40px;
 }
 .ace-nav > li:nth-last-child(3) > .dropdown-menu.pull-right:before,
 .ace-nav > li:nth-last-child(3) > .dropdown-menu.pull-right:after {
  right:60px;
 }

 .user-menu.dropdown-close.pull-right {
	right: 0 !important;
 }
}

@media only screen and (max-width: @screen-xs) {
 .ace-nav > li:nth-last-child(4) > .dropdown-menu.pull-right {
	right:-120px;
 }
 .ace-nav > li:nth-last-child(4) > .dropdown-menu.pull-right:before,
 .ace-nav > li:nth-last-child(4) > .dropdown-menu.pull-right:after {
	right:140px;
 }
 
 .ace-nav > li:nth-last-child(3) > .dropdown-menu.pull-right {
	right:-80px;
 }
 .ace-nav > li:nth-last-child(3) > .dropdown-menu.pull-right:before,
 .ace-nav > li:nth-last-child(3) > .dropdown-menu.pull-right:after {
	right:100px;
 }
 
 .ace-nav > li:nth-last-child(2) > .dropdown-menu.pull-right {
	right:-50px;
 }
 .ace-nav > li:nth-last-child(2) > .dropdown-menu.pull-right:before,
 .ace-nav > li:nth-last-child(2) > .dropdown-menu.pull-right:after {
	right:70px;
 }
}


@media only screen and (max-width: @screen-topbar-down) {
 .ace-nav > li:nth-last-child(4) > .dropdown-menu.pull-right {
	left:-5px;
	right:auto;
 }
 .ace-nav > li:nth-last-child(4) > .dropdown-menu.pull-right:before,
 .ace-nav > li:nth-last-child(4) > .dropdown-menu.pull-right:after {
	right:auto;
	left:25px;
 }
 
 .ace-nav > li:nth-last-child(3) > .dropdown-menu.pull-right {
	left:-60px;
	right:auto;
 }
 .ace-nav > li:nth-last-child(3) > .dropdown-menu.pull-right:before,
 .ace-nav > li:nth-last-child(3) > .dropdown-menu.pull-right:after {
	left:80px;
	right:auto;
 }
 
 .ace-nav > li:nth-last-child(2) > .dropdown-menu.pull-right {
	left:-110px;
	right:auto;
 }
 .ace-nav > li:nth-last-child(2) > .dropdown-menu.pull-right:before,
 .ace-nav > li:nth-last-child(2) > .dropdown-menu.pull-right:after {
	left:130px;
	right:auto;
 }
}


/* move the icons to the line below */
@media only screen and (max-width: @screen-topbar-down) {
 .navbar .navbar-header.pull-left {
  display:block;
  float:none !important;
 }
 .navbar .navbar-header.pull-right {
  display:block;
  float:none !important;
 }

 /*.navbar .navbar-inner {
  border-bottom-width:0;
 }*/
 .ace-nav {
	display:block;
	float:none !important;
	text-align:center;
	
	background-color:#404040;
	
	border:1px solid #DDD;
	border-width:1px 0;

	letter-spacing: -3px;
 }
 .ace-nav > li{
	display:inline-block;
	float:none !important;
	text-align:left;

	letter-spacing: normal;
 }
 .ace-nav > li:first-child{
	border-left:1px solid #DDD;
 }
 .ace-nav > li:last-child{
	border-right:1px solid #DDD;
 }
}

@media only screen and (max-width: @screen-tiny) {
 .ace-nav > li  > a {
	padding:0 5px !important;
 }
}






 .user-menu > li > a {
	padding:4px 12px;

	> [class*="icon-"] {
		margin-right:6px;
		font-size:120%;
	}
 }

 .user-info {
	max-width:100px;
	display:inline-block;
	overflow:hidden;
	text-overflow:ellipsis;
	white-space:nowrap;
	text-align:left;

	vertical-align:top;
	line-height:15px; 
	position:relative; top:6px;

	 small {
		display:block;
	 }
 }
 
 @media (min-width: @screen-topbar-down-min) and (max-width: @screen-xs) , (max-width: 360px) {
	.user-menu {
		padding-top:42px !important;
	}
	.ace-nav .nav-user-photo {
		margin-right:0;
	}
	.user-info {
		position:absolute !important;
		margin-top:40px; margin-left:1px; right:2px;

		z-index:1032;
		color:#777; font-size:14px;
		width:156px;
		max-width:156px;
		
		padding-left:8px;
		padding-right:8px;
		height:32px;
		line-height:26px !important;


		display:none;

		border-bottom:1px solid #E5E5E5;

		text-align:center;

		vertical-align:none;
		line-height:normal; 
	}

	.user-info > small {
		display:inline;
		opacity:1;
	}

	li.open .user-info {
		display:inline-block;
	}
 }
 