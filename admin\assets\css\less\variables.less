//basic variables
@baseFontSize:13px;
@sansFontFamily:"Open Sans";

@screen-xs-min:              (@screen-xs + 1);//!ignore
@screen-sm-min:              (@screen-sm + 1);//!ignore
@screen-md-min:              (@screen-md + 1);//!ignore
@screen-lg-min:              (@screen-lg + 1);//!ignore
@screen-topbar-down:         460px;//The point to move top menu buttons down in default layout
@screen-topbar-down-min:	(@screen-topbar-down + 1);//!ignore

@screen-tiny:	320px;//!ignore


//general.less variables
@body-bg:#E4E6E9;
@text-color:#393939;


//ace top nav/bar
@navbar-mh:45px;//navbar's min-height
@navbar-bg:#438EB9;//navbar background color
@navbar-text-color:#FFF;
@brand-size:24px;//brand logo text size

@ace-nav-default:#2E6589;//ace nav button default background color
@ace-nav-grey:#555;//ace nav button grey background color
@ace-nav-purple:#892E65;//ace nav button purple background color
@ace-nav-green:#2E8965;
@ace-nav-red:#B74635;
@ace-nav-light-green:#9ABC32;
@ace-nav-light-purple:#CB6FD7;
@ace-nav-light-orange:#F79263;
@ace-nav-light-blue:#62A8D1;
@ace-nav-light-blue2:#42A8E1;
@ace-nav-light-pink:#F4DAE5;
@ace-nav-dark:#404040;
@ace-nav-white-opaque:rgba(255,255,255,0.8);
@ace-nav-dark-opaque:rgba(0,0,0,0.2);

@navbar-dropdown-width:240px;
@navbar-dropdown-shadow:~"0 2px 4px rgba(30, 30, 100, 0.25)";



//sidebar
@menu-button-text: "MENU";

@sidebar-width:190px;//sidebar width
@sidebar-min-width:43px;//sidebar minimum width
@menu-focus-color:#1963AA;
@menu-active-color:#2B7DBC;

@submenu-left-border:mix(#BCCFE0 , #7EAACB);
@submenu-active-left-border:mix(mix(#BCCFE0 , #7EAACB) , #7EAACB);

@menumin-border:#CCC;



//breadcrumb
@breadcrumb-height:41px;
@breadcrumb-arrow-color:#B2B6BF;



// heading font size
@h1-size: 32;
@h2-size: 26;
@h3-size: 22;
@h4-size: 18;
@h5-size: 15;
@h6-size: 13;




//some text colors
@ace-dark:#333;
@ace-white:#FFF;
@ace-red:#DD5A43;
@ace-light-red:#FF7777;
@ace-blue:#478FCA;
@ace-light-blue:#93CBF9;
@ace-green:#69AA46;
@ace-light-green:#B0D877;
@ace-orange:#FF892A;
@ace-orange2:#FEB902;
@ace-light-orange:#FCAC6F;
@ace-purple:#A069C3;
@ace-pink:#C6699F;
@ace-pink2:#D6487E;
@ace-brown:brown;
@ace-grey:#777;
@ace-light-grey:#BBB;




//button colors
@btn-default:#ABBAC3;
@btn-default-hover:#8B9AA3;

@btn-primary:#428BCA;
@btn-primary-hover:#1B6AAA;

@btn-info:#6FB3E0;
@btn-info-hover:#4F99C6;

@btn-success:#87B87F;
@btn-success-hover:#629B58;

@btn-warning:#FFB752;
@btn-warning-hover:#E59729;

@btn-danger:#D15B47;
@btn-danger-hover:#B74635;

@btn-inverse:#555555;
@btn-inverse-hover:#303030;

@btn-pink:#D6487E;
@btn-pink-hover:#B73766;

@btn-purple:#9585BF;
@btn-purple-hover:#7461AA;

@btn-yellow:#FEE188;
@btn-yellow-hover:#F7D05B;
@btn-yellow-color:#996633;

@btn-light:#E7E7E7;
@btn-light-hover:#D9D9D9;
@btn-light-color:#888;

@btn-grey:#A0A0A0;
@btn-grey-hover:#888888;

//active state:
@btn-active-color:#EFE5B5;
@btn-yellow-active-border:#C96338;

@btn-link-color:#0088CC;




//application button colors
@btn-app-default-1:#BCC9D5;
@btn-app-default-2:#ABBAC3;

@btn-app-primary-1:#3B98D6;
@btn-app-primary-2:#197EC1;

@btn-app-info-1:#75B5E6;
@btn-app-info-2:#5BA4D5;

@btn-app-success-1:#8EBF60;
@btn-app-success-2:#7DAA50;

@btn-app-danger-1:#D55B52;
@btn-app-danger-2:#D12723;

@btn-app-warning-1:#FFBF66;
@btn-app-warning-2:#FFA830;

@btn-app-purple-1:#A696CE;
@btn-app-purple-2:#8A7CB4;

@btn-app-pink-1:#DB5E8C;
@btn-app-pink-2:#CE3970;

@btn-app-inverse-1:#555555;
@btn-app-inverse-2:#333333;

@btn-app-grey-1:#898989;
@btn-app-grey-2:#696969;

@btn-app-light-1:#F4F4F4;
@btn-app-light-2:#E6E6E6;

@btn-app-yellow-1:#FFE8A5;
@btn-app-yellow-2:#FCD76A;

@btn-app-active:#FFF;
@btn-app-yellow-color:#963;
@btn-app-yellow-border:#FEE188;
@btn-app-light-color:#5A5A5A;
@btn-app-light-active:#515151;




//label & badge colors
@label-default:#ABBAC3;
@label-primary:@btn-primary;
@label-info:#3A87AD;
@label-success:#82AF6F;
@label-danger:#D15B47;
@label-important:@label-danger;//!ignore
@label-warning:#F89406;
@label-inverse:#333333;
@label-pink:#D6487E;
@label-purple:#9585BF;
@label-yellow:#FEE188;
@label-light:#E7E7E7;
@label-grey:@btn-grey;



//menu colors
@dropdown-menu:#4F99C6;
@dropdown-default:@btn-default;
@dropdown-primary:@btn-primary;
@dropdown-info:@btn-info;
@dropdown-success:@btn-success;
@dropdown-warning:#FFA24D;
@dropdown-danger:@btn-danger;
@dropdown-inverse:@btn-inverse;
@dropdown-pink:@btn-pink;
@dropdown-purple:@btn-purple;
@dropdown-grey:@btn-grey;
@dropdown-light:@btn-light;
@dropdown-lighter:#EFEFEF;
@dropdown-lightest:#F3F3F3;
@dropdown-yellow:@btn-yellow;
@dropdown-yellow2:#F9E8B3;
@dropdown-light-blue:#ECF3F9;

//slider colors
@slider-color:#4AA4CE;
@slider-green:#8BBC67;
@slider-red:#D36E6E;
@slider-purple:#AC68BA;
@slider-orange:#EFAD62;
@slider-dark:#606060;
@slider-pink:@btn-pink;


//infobox colors
@infobox-purple:#6F3CC4;
@infobox-purple2:#5F47B0;
@infobox-pink:#CB6FD7;
@infobox-blue:#6FB3E0;
@infobox-blue2:#3983C2;
@infobox-blue3:#1144EB;
@infobox-red:#D53F40;
@infobox-brown:#C67A3E;
@infobox-light-brown:#CEBEA5;
@infobox-wood:#7B3F25;
@infobox-orange:#E8B110;
@infobox-orange2:#F79263;
@infobox-green:#9ABC32;
@infobox-green2:#0490A6;
@infobox-grey:#999999;
@infobox-black:#393939;


//widget colors
@widget-blue:#307ECC;
@widget-blue2:#5090C1;
@widget-blue3:#6379AA;
@widget-green:#82AF6F;
@widget-green2:#2E8965;
@widget-green3:#4EBC30;
@widget-red:#E2755F;
@widget-red2:#E04141;
@widget-red3:#D15B47;
@widget-purple:#7E6EB0;
@widget-pink:#CE6F9E;
@widget-dark:#404040;
@widget-grey:#848484;

@widget-orange:#FFC657;
@widget-orange-txt:#855D10;
@widget-orange-border:#E8B10D;




//form
@help-button-bg:#65BCDA;
@input-border:#D5D5D5;
@input-bg:#FFF;
@input-color:#858585;

@input-focus-border:#F59942;
@input-focus-bg:#FFF;
@input-focus-color:#696969;
//@input-focus-shadow:~"0px 0px 0px 2px rgba(245, 153, 66, 0.3)";



//tabs & accordion
@tab-border:#C5D0DC;
@tab-active-color:#576373;
@tab-active-border:#4C8FBD;
@tab-hover-color:#4C8FBD;



//tooltip
@tooltip-color:#333;
@tooltip-error-color:#C94D32;
@tooltip-success-color:#629B58;
@tooltip-warning-color:#ED9421;
@tooltip-info-color:#4B89AA;


//progress bar
@progress-color:#2A91D8;
@progress-danger:#CA5952;
@progress-success:#59A84B;
@progress-warning:#F2BB46;
@progress-pink:#D6487E;
@progress-purple:#9585BF;
@progress-yellow:#FFD259;
@progress-inverse:#404040;
@progress-grey:#8A8A8A;





//widget
@widget-header-color:#669FC7;//text color
@tag-bg:#91B8D0;



//items
@item-list-orange-border:#E8B110;
@item-list-orange2-border:#F79263;
@item-list-red-border:#D53F40;
@item-list-red2-border:#D15B47;
@item-list-green-border:#9ABC32;
@item-list-green2-border:#0490A6;
@item-list-blue-border:@btn-info-hover;
@item-list-blue2-border:#3983C2;
@item-list-blue3-border:#1144EB;
@item-list-pink-border:#CB6FD7;
@item-list-purple-border:#6F3CC4;
@item-list-black-border:#505050;
@item-list-grey-border:#A0A0A0;
@item-list-brown-border:brown;
@item-list-default-border:@btn-default;
