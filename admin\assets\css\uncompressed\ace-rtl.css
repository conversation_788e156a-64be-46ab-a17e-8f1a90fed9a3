.rtl [class*="col-xs-"] {
  float: right;
}
@media (min-width: 768px) {
  .rtl [class*="col-sm-"] {
    float: right;
  }
}
@media (min-width: 992px) {
  .rtl [class*="col-md-"] {
    float: right;
  }
}
@media (min-width: 1200px) {
  .rtl [class*="col-lg-"] {
    float: right;
  }
}
@media (min-width: 768px) {
  .rtl [class*="col-sm-push-"] {
    left: auto;
  }
  .rtl [class*="col-sm-pull-"] {
    right: auto;
  }
  .rtl [class*="col-sm-offset-"] {
    margin-left: auto;
  }
  .rtl .col-sm-push-1 {
    right: 8.33333%;
  }
  .rtl .col-sm-push-2 {
    right: 16.6666%;
  }
  .rtl .col-sm-push-3 {
    right: 25%;
  }
  .rtl .col-sm-push-4 {
    right: 33.3333%;
  }
  .rtl .col-sm-push-5 {
    right: 41.6666%;
  }
  .rtl .col-sm-push-6 {
    right: 50%;
  }
  .rtl .col-sm-push-7 {
    right: 58.3333%;
  }
  .rtl .col-sm-push-8 {
    right: 66.6666%;
  }
  .rtl .col-sm-push-9 {
    right: 75%;
  }
  .rtl .col-sm-push-10 {
    right: 83.3333%;
  }
  .rtl .col-sm-push-11 {
    right: 91.6666%;
  }
  .rtl .col-sm-pull-1 {
    left: 8.33333%;
  }
  .rtl .col-sm-pull-2 {
    left: 16.6666%;
  }
  .rtl .col-sm-pull-3 {
    left: 25%;
  }
  .rtl .col-sm-pull-4 {
    left: 33.3333%;
  }
  .rtl .col-sm-pull-5 {
    left: 41.6666%;
  }
  .rtl .col-sm-pull-6 {
    left: 50%;
  }
  .rtl .col-sm-pull-7 {
    left: 58.3333%;
  }
  .rtl .col-sm-pull-8 {
    left: 66.6666%;
  }
  .rtl .col-sm-pull-9 {
    left: 75%;
  }
  .rtl .col-sm-pull-10 {
    left: 83.3333%;
  }
  .rtl .col-sm-pull-11 {
    left: 91.6666%;
  }
  .rtl .col-sm-offset-1 {
    margin-right: 8.33333%;
  }
  .rtl .col-sm-offset-2 {
    margin-right: 16.6666%;
  }
  .rtl .col-sm-offset-3 {
    margin-right: 25%;
  }
  .rtl .col-sm-offset-4 {
    margin-right: 33.3333%;
  }
  .rtl .col-sm-offset-5 {
    margin-right: 41.6666%;
  }
  .rtl .col-sm-offset-6 {
    margin-right: 50%;
  }
  .rtl .col-sm-offset-7 {
    margin-right: 58.3333%;
  }
  .rtl .col-sm-offset-8 {
    margin-right: 66.6666%;
  }
  .rtl .col-sm-offset-9 {
    margin-right: 75%;
  }
  .rtl .col-sm-offset-10 {
    margin-right: 83.3333%;
  }
  .rtl .col-sm-offset-11 {
    margin-right: 91.6666%;
  }
}
@media (min-width: 992px) {
  .rtl [class*="col-md-push-"] {
    left: auto;
  }
  .rtl [class*="col-md-pull-"] {
    right: auto;
  }
  .rtl [class*="col-md-offset-"] {
    margin-left: auto;
  }
  .rtl .col-md-push-1 {
    right: 8.33333%;
  }
  .rtl .col-md-push-2 {
    right: 16.6666%;
  }
  .rtl .col-md-push-3 {
    right: 25%;
  }
  .rtl .col-md-push-4 {
    right: 33.3333%;
  }
  .rtl .col-md-push-5 {
    right: 41.6666%;
  }
  .rtl .col-md-push-6 {
    right: 50%;
  }
  .rtl .col-md-push-7 {
    right: 58.3333%;
  }
  .rtl .col-md-push-8 {
    right: 66.6666%;
  }
  .rtl .col-md-push-9 {
    right: 75%;
  }
  .rtl .col-md-push-10 {
    right: 83.3333%;
  }
  .rtl .col-md-push-11 {
    right: 91.6666%;
  }
  .rtl .col-md-pull-1 {
    left: 8.33333%;
  }
  .rtl .col-md-pull-2 {
    left: 16.6666%;
  }
  .rtl .col-md-pull-3 {
    left: 25%;
  }
  .rtl .col-md-pull-4 {
    left: 33.3333%;
  }
  .rtl .col-md-pull-5 {
    left: 41.6666%;
  }
  .rtl .col-md-pull-6 {
    left: 50%;
  }
  .rtl .col-md-pull-7 {
    left: 58.3333%;
  }
  .rtl .col-md-pull-8 {
    left: 66.6666%;
  }
  .rtl .col-md-pull-9 {
    left: 75%;
  }
  .rtl .col-md-pull-10 {
    left: 83.3333%;
  }
  .rtl .col-md-pull-11 {
    left: 91.6666%;
  }
  .rtl .col-md-offset-1 {
    margin-right: 8.33333%;
  }
  .rtl .col-md-offset-2 {
    margin-right: 16.6666%;
  }
  .rtl .col-md-offset-3 {
    margin-right: 25%;
  }
  .rtl .col-md-offset-4 {
    margin-right: 33.3333%;
  }
  .rtl .col-md-offset-5 {
    margin-right: 41.6666%;
  }
  .rtl .col-md-offset-6 {
    margin-right: 50%;
  }
  .rtl .col-md-offset-7 {
    margin-right: 58.3333%;
  }
  .rtl .col-md-offset-8 {
    margin-right: 66.6666%;
  }
  .rtl .col-md-offset-9 {
    margin-right: 75%;
  }
  .rtl .col-md-offset-10 {
    margin-right: 83.3333%;
  }
  .rtl .col-md-offset-11 {
    margin-right: 91.6666%;
  }
}
@media (min-width: 1200px) {
  .rtl [class*="col-lg-push-"] {
    left: auto;
  }
  .rtl [class*="col-lg-pull-"] {
    right: auto;
  }
  .rtl [class*="col-lg-offset-"] {
    margin-left: auto;
  }
  .rtl .col-lg-push-1 {
    right: 8.33333%;
  }
  .rtl .col-lg-push-2 {
    right: 16.6666%;
  }
  .rtl .col-lg-push-3 {
    right: 25%;
  }
  .rtl .col-lg-push-4 {
    right: 33.3333%;
  }
  .rtl .col-lg-push-5 {
    right: 41.6666%;
  }
  .rtl .col-lg-push-6 {
    right: 50%;
  }
  .rtl .col-lg-push-7 {
    right: 58.3333%;
  }
  .rtl .col-lg-push-8 {
    right: 66.6666%;
  }
  .rtl .col-lg-push-9 {
    right: 75%;
  }
  .rtl .col-lg-push-10 {
    right: 83.3333%;
  }
  .rtl .col-lg-push-11 {
    right: 91.6666%;
  }
  .rtl .col-lg-pull-1 {
    left: 8.33333%;
  }
  .rtl .col-lg-pull-2 {
    left: 16.6666%;
  }
  .rtl .col-lg-pull-3 {
    left: 25%;
  }
  .rtl .col-lg-pull-4 {
    left: 33.3333%;
  }
  .rtl .col-lg-pull-5 {
    left: 41.6666%;
  }
  .rtl .col-lg-pull-6 {
    left: 50%;
  }
  .rtl .col-lg-pull-7 {
    left: 58.3333%;
  }
  .rtl .col-lg-pull-8 {
    left: 66.6666%;
  }
  .rtl .col-lg-pull-9 {
    left: 75%;
  }
  .rtl .col-lg-pull-10 {
    left: 83.3333%;
  }
  .rtl .col-lg-pull-11 {
    left: 91.6666%;
  }
  .rtl .col-lg-offset-1 {
    margin-right: 8.33333%;
  }
  .rtl .col-lg-offset-2 {
    margin-right: 16.6666%;
  }
  .rtl .col-lg-offset-3 {
    margin-right: 25%;
  }
  .rtl .col-lg-offset-4 {
    margin-right: 33.3333%;
  }
  .rtl .col-lg-offset-5 {
    margin-right: 41.6666%;
  }
  .rtl .col-lg-offset-6 {
    margin-right: 50%;
  }
  .rtl .col-lg-offset-7 {
    margin-right: 58.3333%;
  }
  .rtl .col-lg-offset-8 {
    margin-right: 66.6666%;
  }
  .rtl .col-lg-offset-9 {
    margin-right: 75%;
  }
  .rtl .col-lg-offset-10 {
    margin-right: 83.3333%;
  }
  .rtl .col-lg-offset-11 {
    margin-right: 91.6666%;
  }
}
.rtl ul,
.rtl ol {
  margin-left: 0;
  margin-right: 25px;
}
.rtl ul.list-unstyled,
.rtl ol.list-unstyled,
.rtl ul.list-inline,
.rtl ol.list-inline {
  margin-right: 0;
}
.rtl li > ul,
.rtl li > ol {
  margin-left: 0;
  margin-right: 18px;
}
.rtl dd {
  margin-left: 0;
  margin-right: 10px;
}
.rtl .dl-horizontal dt {
  float: right;
  clear: right;
  text-align: left;
}
.rtl .dl-horizontal dd {
  margin-left: 0;
  margin-right: 180px;
}
.rtl blockquote p,
.rtl blockquote small {
  text-align: left;
}
.rtl blockquote small:before {
  content: "";
}
.rtl blockquote small:after {
  content: "\00A0 \2014";
}
.rtl blockquote.pull-right p,
.rtl blockquote.pull-right small {
  text-align: right;
}
.rtl blockquote.pull-right small:after {
  content: "";
}
.rtl blockquote.pull-right small:before {
  content: "\2014 \00A0";
}
.rtl .radio,
.rtl .checkbox {
  padding-left: 0;
  padding-right: 20px;
}
.rtl .radio input[type="radio"],
.rtl .checkbox input[type="checkbox"] {
  float: right;
  margin-left: 0;
  margin-right: -20px;
}
.rtl .radio.inline + .radio.inline,
.rtl .checkbox.inline + .checkbox.inline {
  margin-left: 0;
  margin-right: 10px;
}
.rtl .help-inline {
  padding-left: 0;
  padding-right: 5px;
}
.rtl .input-group .input-group-addon:first-child {
  border-left-width: 0;
  border-right: 1px solid #CCCCCC;
}
.rtl .input-group .input-group-addon:last-child {
  border-right-width: 0;
  border-left: 1px solid #CCCCCC;
}
.rtl input.search-query {
  padding-left: 14px;
  padding-left: 4px \9;
  padding-right: 14px;
  padding-right: 4px \9;
}
.rtl .form-search .radio,
.rtl .form-search .checkbox,
.rtl .form-inline .radio,
.rtl .form-inline .checkbox {
  padding-right: 0;
}
.rtl .form-search .radio input[type="radio"],
.rtl .form-search .checkbox input[type="checkbox"],
.rtl .form-inline .radio input[type="radio"],
.rtl .form-inline .checkbox input[type="checkbox"] {
  float: right;
  margin-right: 0;
  margin-left: 3px;
}
.rtl .form-horizontal .control-label {
  text-align: left;
}
.rtl .btn-group + .btn-group {
  margin-left: auto;
  margin-right: 5px;
}
.rtl .btn-group > .btn,
.rtl .btn-group-vertical > .btn {
  float: right;
}
.rtl .dropdown-menu {
  margin: 2px 0 0;
}
.rtl .dropdown .caret {
  margin-left: 0;
  margin-right: 2px;
}
.rtl .close {
  float: left;
}
.rtl .table-header .close {
  margin-right: auto;
  margin-left: 6px;
}
.rtl .alert .close {
  float: left;
}
.rtl .nav {
  margin-right: 0;
}
.rtl .nav-tabs > li,
.rtl .nav-pills > li {
  float: right;
}
.rtl .nav-pills > li > a {
  margin-right: 0;
  margin-left: 2px;
}
.rtl .nav-stacked > li {
  float: none;
}
.rtl .nav-tabs > li > a {
  margin-right: 0;
  margin-left: -1px;
}
.rtl .tabs-left > .nav-tabs > li,
.rtl .tabs-right > .nav-tabs > li {
  float: none;
}
.rtl .navbar {
  direction: rtl;
  text-align: right;
}
.rtl .navbar .navbar-brand {
  float: right;
}
.rtl .navbar-search {
  float: right;
}
.rtl .navbar-text {
  float: right;
}
.rtl ul.pagination {
  margin-right: 0;
}
.rtl .pager {
  margin-right: 0;
}
.rtl .pager .next > a,
.rtl .pager .next > span {
  float: left;
}
.rtl .pager .previous > a,
.rtl .pager .previous > span {
  float: right;
}
.rtl .modal {
  direction: rtl;
  text-align: right;
}
.rtl .modal-footer {
  text-align: left;
}
.rtl .modal-footer .btn + .btn {
  margin-left: 0;
  margin-right: 5px;
}
.rtl .popover.bottom .arrow:after,
.rtl .popover.top .arrow:after {
  margin-right: -10px;
  margin-left: auto;
}
.rtl .popover-content,
.rtl .tooltip-inner {
  text-align: right;
  direction: rtl;
}
.rtl .thumbnails {
  margin-left: 0;
  margin-right: -24px;
}
.rtl .row .thumbnails {
  margin-right: 0;
}
.rtl .thumbnails > li {
  float: right;
  margin-left: 0;
  margin-right: 24px;
}
.rtl .media-list {
  margin-right: 0;
}
.rtl .main-container {
  direction: rtl;
  text-align: right;
}
.rtl .main-content {
  margin-right: 190px;
  margin-left: 0;
}
.rtl li > ul.margin,
.rtl li > ol.margin {
  margin-left: 0;
  margin-right: 18px;
}
.rtl .ace-nav > li {
  float: right !important;
  border-left: none;
  border-right: 1px solid #DDD;
}
.rtl .ace-nav > li:first-child {
  border-right: none;
}
.rtl .ace-nav > li > a > .badge {
  left: auto;
  right: 2px;
}
.rtl .ace-nav > li.no-border {
  border: none;
}
.rtl .ace-nav > li.margin-4 {
  margin-left: 0;
  margin-right: 4px;
}
.rtl .ace-nav > li.margin-3 {
  margin-left: 0;
  margin-right: 3px;
}
.rtl .ace-nav > li.margin-2 {
  margin-left: 0;
  margin-right: 2px;
}
.rtl .ace-nav > li.margin-1 {
  margin-left: 0;
  margin-right: 1px;
}
.rtl .ace-nav .nav-user-photo {
  margin: -4px 0 0 8px;
}
.rtl .ace-nav .dropdown-menu.dropdown-closer {
  left: 0;
}
.rtl .breadcrumbs {
  padding: 0 0 0 12px;
}
.rtl .breadcrumbs.fixed,
.rtl .breadcrumbs.breadcrumbs-fixed {
  position: fixed;
  left: 0;
  right: 190px;
}
.rtl .breadcrumb {
  margin: 0 12px 0 22px;
}
.rtl .breadcrumb .icon-home {
  margin-left: 2px;
  margin-right: 4px;
}
.rtl .breadcrumb > li + li:before {
  content: "\f104";
  padding: 0 2px 0 5px;
}
.rtl .breadcrumb > li + li:last-child:before {
  display: none;
}
.rtl .breadcrumb > li + li:last-child:after {
  display: inline;
  font-family: FontAwesome;
  font-size: 14px;
  content: "\f104";
  color: #b2b6bf;
  margin-left: 2px;
  padding: 0 2px 0 5px;
  position: relative;
  top: 1px;
}
.rtl .nav-search {
  left: 22px;
  right: auto;
}
.rtl .sidebar > .nav-search.menu-min .nav-search .form-search {
  left: auto;
  right: 5px;
}
.rtl .sidebar {
  float: right;
  border-width: 0 0 0 1px;
}
.rtl .sidebar:before {
  border-width: 0 0 0 1px;
}
.rtl .sidebar.fixed,
.rtl .sidebar.sidebar-fixed {
  left: auto;
  right: 0;
}
.rtl .sidebar.fixed:before,
.rtl .sidebar.sidebar-fixed:before {
  left: auto;
  right: 0;
}
.rtl .nav-list > li > a {
  padding: 0 7px 0 16px;
}
.rtl .nav-list > li > a:hover:before {
  left: auto;
  right: 0;
}
.rtl .nav-list > li a > .arrow {
  left: 9px;
  right: auto;
}
.rtl .nav-list > li.active:after {
  left: -2px;
  right: auto;
  border-width: 0 0 0 2px;
}
.rtl .nav-list > li .submenu > li {
  margin-left: 0;
  margin-right: 0;
}
.rtl .nav-list > li .submenu > li > a {
  padding: 7px 37px 8px 0;
}
.rtl .nav-list > li .submenu > li a > [class*="icon-"]:first-child {
  left: auto;
  right: 10px;
}
.rtl .nav-list > li > .submenu > li:before {
  left: auto;
  right: 18px;
}
.rtl .nav-list > li > .submenu:before {
  left: auto;
  right: 18px;
  border-width: 0 1px 0 0;
}
.rtl .nav-list > li.active > .submenu:before {
  border-right-color: #8eb3d0;
}
.rtl .nav-list li.active > a:after {
  left: 0;
  right: auto;
  border-left-color: #2b7dbc;
  border-right-color: transparent;
}
.rtl .nav-list a .badge,
.rtl .nav-list a .label {
  right: auto;
  left: 11px;
}
.rtl .nav-list a.dropdown-toggle .badge,
.rtl .nav-list a.dropdown-toggle .label {
  right: auto;
  left: 28px;
}
.rtl .menu-min .nav-list a .badge,
.rtl .menu-min .nav-list a .label {
  left: auto;
  right: 4px;
}
.rtl .sidebar.menu-min + .main-content {
  margin-left: auto;
  margin-right: 43px;
}
.rtl .sidebar.menu-min + .main-content .breadcrumbs.fixed,
.rtl .sidebar.menu-min + .main-content .breadcrumbs.breadcrumbs-fixed {
  left: 0;
  right: 43px;
}
.rtl .menu-min .nav-list > li > a > .menu-text {
  left: auto;
  right: 42px;
  padding-left: 0;
  padding-right: 12px;
  -webkit-box-shadow: -2px 1px 2px 0 rgba(0, 0, 0, 0.2);
  box-shadow: -2px 1px 2px 0 rgba(0, 0, 0, 0.2);
}
.rtl .menu-min .nav-list > li > a.dropdown-toggle > .menu-text {
  left: auto;
  right: 43px;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.rtl .menu-min .nav-list > li.active > a > .menu-text {
  border-left-color: #cccccc;
  border-right-color: #1963aa;
}
.rtl .menu-min .nav-list > li > .submenu {
  -webkit-box-shadow: -2px 1px 2px 0 rgba(0, 0, 0, 0.2);
  box-shadow: -2px 1px 2px 0 rgba(0, 0, 0, 0.2);
  left: auto;
  right: 42px;
}
.rtl .menu-min .nav-list > li > .submenu li > a {
  margin-left: auto;
  margin-right: 0;
  padding-left: 0;
  padding-right: 24px;
}
.rtl .menu-min .nav-list > li > .submenu li > a > [class*="icon-"]:first-child {
  left: auto;
  right: 4px;
}
.rtl .menu-min .nav-list > li.active > .submenu {
  border-left-color: #cccccc;
  border-right-color: #1963aa;
}
.rtl .menu-min .sidebar-shortcuts-large {
  -webkit-box-shadow: -2px 1px 2px 0 rgba(0, 0, 0, 0.2);
  box-shadow: -2px 1px 2px 0 rgba(0, 0, 0, 0.2);
  left: auto;
  right: 42px;
}
.rtl .nav-list > li > .submenu a > .arrow {
  left: 11px;
  right: auto;
}
.rtl .nav-list > li > .submenu li > .submenu > li > a > .arrow {
  left: 12px;
  right: auto;
}
.rtl .nav-list > li > .submenu li > .submenu > li > a {
  margin-left: auto;
  padding-left: 0;
  margin-right: 20px;
  padding-right: 22px;
}
.rtl .nav-list > li > .submenu li > .submenu > li > .submenu > li > a {
  margin-left: auto;
  padding-left: 0;
  margin-right: 20px;
  padding-right: 38px;
}
.rtl .menu-min .nav-list > li > .submenu li > .submenu > li > a {
  margin-right: 0px;
  padding-right: 30px;
}
.rtl .menu-min .nav-list > li > .submenu li > .submenu > li > .submenu > li > a {
  margin-right: 0px;
  padding-right: 45px;
}
.rtl button.btn:active {
  left: -1px;
}
.rtl .btn.disabled:active,
.rtl .btn[disabled]:active {
  left: 0;
}
.rtl .btn > [class*="icon-"] {
  margin-left: 4px;
  margin-right: 0;
}
.rtl .btn > [class*="icon-"].icon-on-right {
  margin-left: 0;
  margin-right: 4px;
}
.rtl .btn > [class*="icon-"].icon-only {
  margin: 0;
}
.rtl .btn-lg > [class*="icon-"] {
  margin-left: 6px;
  margin-right: 0;
}
.rtl .btn-lg > [class*="icon-"].icon-on-right {
  margin-left: 0;
  margin-right: 6px;
}
.rtl .btn-sm > [class*="icon-"] {
  margin-left: 3px;
  margin-right: 0;
}
.rtl .btn-sm > [class*="icon-"].icon-on-right {
  margin-left: 0;
  margin-right: 3px;
}
.rtl .btn-xs > [class*="icon-"],
.rtl.btn-minier > [class*="icon-"] {
  margin-left: 2px;
  margin-right: 0;
}
.rtl .btn-xs > [class*="icon-"].icon-on-right,
.rtl.btn-minier > [class*="icon-"].icon-on-right {
  margin-left: 0;
  margin-right: 2px;
}
.rtl .btn-group > .btn > .caret {
  margin-left: 0;
  margin-right: 1px;
}
.rtl .dropdown-menu.dropdown-only-icon > li {
  float: right;
}
.rtl .dropdown-light .dropdown-submenu:hover > a:after,
.rtl .dropdown-lighter .dropdown-submenu:hover > a:after {
  border-left-color: transparent;
  border-right-color: #444;
}
.rtl .dropdown-navbar > li > [class*="icon-"],
.rtl .dropdown-navbar > li > a > [class*="icon-"] {
  margin-right: 0 !important;
  margin-left: 5px !important;
}
.rtl .dropdown-navbar [class*="btn"][class*="icon-"] {
  margin: 0 0 0 5px;
}
.rtl .dropdown-navbar .msg-photo {
  margin-left: 6px;
  margin-right: 0;
}
.rtl .dropdown-navbar .user-menu > li > a > [class*="icon-"] {
  margin-left: 6px;
  margin-right: 0;
}
.rtl .help-button {
  margin-left: 0;
  margin-right: 4px;
}
.rtl .form-search .radio [type=radio] + label,
.rtl .form-inline .radio [type=radio] + label,
.rtl .form-search .checkbox [type=checkbox] + label,
.rtl .form-inline .checkbox [type=checkbox] + label {
  float: right;
  margin-left: 0;
  margin-right: -20px;
}
.form-search .rtl .form-search .radio [type=radio] + label,
.form-search .rtl .form-inline .radio [type=radio] + label,
.form-search .rtl .form-search .checkbox [type=checkbox] + label,
.form-search .rtl .form-inline .checkbox [type=checkbox] + label,
.form-inline .rtl .form-search .radio [type=radio] + label,
.form-inline .rtl .form-inline .radio [type=radio] + label,
.form-inline .rtl .form-search .checkbox [type=checkbox] + label,
.form-inline .rtl .form-inline .checkbox [type=checkbox] + label {
  margin-right: 0;
  margin-left: 3px;
}
.rtl input[type=checkbox].ace + .lbl::before,
.rtl input[type=radio].ace + .lbl::before {
  margin-right: 0;
  margin-left: 1px;
}
.rtl input[type=checkbox].ace.ace-switch + .lbl::before,
.rtl input[type=radio].ace.ace-switch + .lbl::before {
  direction: ltr;
  text-align: left;
}
.rtl .ace-file-input .file-label:before {
  right: auto;
  left: 0;
}
.rtl .ace-file-input .file-label .file-name {
  padding-left: 0;
  padding-right: 30px;
}
.rtl .ace-file-input .file-label.selected {
  left: 16px;
  right: 0;
}
.rtl .ace-file-input .file-label [class*="icon-"] {
  right: 0;
  left: auto;
}
.rtl .ace-file-input .remove {
  left: -8px;
  right: auto;
}
.rtl .ace-file-multiple .file-label.selected .file-name [class*="icon-"] {
  right: 0;
  left: auto;
}
.rtl .ace-file-multiple .file-label .file-name {
  padding: 0;
  text-align: right;
}
.rtl .ace-file-multiple .file-label .file-name img {
  margin: 4px 1px 4px 8px;
}
.rtl .ace-file-multiple .file-label .file-name.large {
  text-align: center;
}
.rtl .ace-file-multiple .file-label .file-name.large img {
  margin: 0;
}
.rtl .ace-file-multiple .remove {
  left: -11px;
  right: auto;
}
.rtl .ace-file-multiple .file-label.selected .file-name [class*="icon-"] {
  margin-left: 4px;
  margin-right: 2px;
}
.rtl .nav-tabs.padding-24 {
  padding-left: 0;
  padding-right: 24px;
}
.rtl .nav-tabs.padding-20 {
  padding-left: 0;
  padding-right: 20px;
}
.rtl .nav-tabs.padding-16 {
  padding-left: 0;
  padding-right: 16px;
}
.rtl .nav-tabs.padding-12 {
  padding-left: 0;
  padding-right: 12px;
}
.rtl .nav-tabs.padding-8 {
  padding-left: 0;
  padding-right: 8px;
}
.rtl .nav-tabs.padding-4 {
  padding-left: 0;
  padding-right: 4px;
}
.rtl .nav-tabs.padding-22 {
  padding-left: 0;
  padding-right: 22px;
}
.rtl .nav-tabs.padding-18 {
  padding-left: 0;
  padding-right: 18px;
}
.rtl .nav-tabs.padding-14 {
  padding-left: 0;
  padding-right: 14px;
}
.rtl .nav-tabs.padding-10 {
  padding-left: 0;
  padding-right: 10px;
}
.rtl .nav-tabs.padding-6 {
  padding-left: 0;
  padding-right: 6px;
}
.rtl .nav-tabs.padding-2 {
  padding-left: 0;
  padding-right: 2px;
}
.rtl .tabs-right > .nav-tabs[class*="padding-"],
.rtl .tabs-left > .nav-tabs[class*="padding-"] {
  padding-right: 0;
}
.rtl .tabs-left > .nav-tabs {
  margin-left: auto;
  margin-right: -1px;
}
.rtl .tabs-left > .nav-tabs > li > a,
.rtl .tabs-left > .nav-tabs > li > a:hover,
.rtl .tabs-left > .nav-tabs > li > a:focus {
  margin: 0 -1px 0 0;
}
.rtl .tabs-left > .nav-tabs > li.active > a,
.rtl .tabs-left > .nav-tabs > li.active > a:hover,
.rtl .tabs-left > .nav-tabs > li.active > a:focus {
  margin: 0 -1px;
}
.rtl .nav-tabs.tab-space-1 > li > a {
  margin-right: auto;
  margin-left: 1px;
}
.rtl .nav-tabs.tab-space-2 > li > a {
  margin-right: auto;
  margin-left: 2px;
}
.rtl .nav-tabs.tab-space-3 > li > a {
  margin-right: auto;
  margin-left: 3px;
}
.rtl .nav-tabs.tab-space-4 > li > a {
  margin-right: auto;
  margin-left: 4px;
}
.rtl .nav-tabs[class*="tab-color-"] > li > a,
.rtl .nav-tabs[class*="tab-color-"] > li > a:focus,
.rtl .nav-tabs[class*="tab-color-"] > li > a:hover {
  margin-right: auto;
  margin-left: 3px;
}
.rtl .accordion-style2.panel-group .panel-heading .accordion-toggle {
  border-width: 0 2px 0 0;
}
.rtl .accordion-style2.panel-group .panel-heading .accordion-toggle.collapsed {
  border-width: 0 1px 0 0;
}
.rtl .table thead:first-child tr th [class*="icon-"]:first-child {
  margin-left: 2px;
  margin-right: 0;
}
.rtl .widget-main.no-padding .table-bordered th:first-child,
.rtl .widget-main.padding-0 .table-bordered th:first-child,
.rtl .widget-main.no-padding .table-bordered td:first-child,
.rtl .widget-main.padding-0 .table-bordered td:first-child {
  border-left-width: 1px;
}
.rtl .widget-main.no-padding .table-bordered th:last-child,
.rtl .widget-main.padding-0 .table-bordered th:last-child,
.rtl .widget-main.no-padding .table-bordered td:last-child,
.rtl .widget-main.padding-0 .table-bordered td:last-child {
  border-left-width: 0;
}
.rtl .table-header {
  padding-left: 0;
  padding-right: 12px;
}
.rtl .dataTables_length {
  margin-left: 0;
  margin-right: 8px;
}
.rtl .dataTables_filter {
  margin-left: 8px;
  margin-right: 0;
  text-align: left;
}
.rtl .dataTables_info {
  margin: 0 12px 0 0;
}
.rtl .dataTables_paginate {
  text-align: left;
}
.rtl .dataTable th[class*=sort]:after {
  float: left;
  margin-right: 0;
  margin-left: 4px;
}
.rtl .dataTables_wrapper > .row > [class*="col-"] {
  float: right;
  margin-left: 0;
  width: 50%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.rtl .widget-box {
  direction: rtl;
  text-align: right;
}
.rtl .widget-header {
  padding-left: 0;
  padding-right: 12px;
}
.rtl .widget-header:after {
  clear: left;
}
.rtl .widget-header-large {
  padding-left: 0;
  padding-right: 18px;
}
.rtl .widget-header-small {
  padding-left: 0;
  padding-right: 10px;
}
.rtl .widget-header > .widget-caption > [class*="icon-"],
.rtl .widget-header > :first-child > [class*="icon-"] {
  margin-right: 0;
  margin-left: 5px;
}
.rtl .widget-toolbar {
  float: left;
}
.rtl .widget-toolbar:before {
  left: auto;
  right: -1px;
  border-width: 0 0 0 1px;
}
.rtl .widget-toolbar > [data-action] > [class*="icon-"] {
  margin-right: auto;
  margin-left: 0;
}
.rtl .widget-box.transparent > .widget-header {
  padding-left: 0;
  padding-right: 3px;
}
.rtl .widget-box.transparent > .widget-header-large {
  padding-left: 0;
  padding-right: 5px;
}
.rtl .widget-box.transparent > .widget-header-small {
  padding-left: 0;
  padding-right: 1px;
}
.rtl [class*="header-color-"] > .widget-toolbar > .nav-tabs > li > a {
  margin-right: 0;
  margin-left: 1px;
}
.rtl .infobox {
  padding: 8px 9px 6px 3px;
  text-align: right;
}
.rtl .infobox > .infobox-icon > [class*="icon-"] {
  padding: 1px 2px 0 1px;
}
.rtl .infobox > .infobox-data {
  text-align: right;
  padding-left: 0;
  padding-right: 8px;
}
.rtl .infobox > .stat {
  left: 20px;
  right: auto;
  padding-left: 18px;
  padding-right: 0;
}
.rtl .infobox > .stat:before {
  left: 4px;
  right: auto;
}
.rtl .infobox > .stat:after {
  left: 1px;
  right: auto;
}
.rtl .infobox > .badge {
  left: 20px;
  right: auto;
}
.rtl .infobox.infobox-dark > .badge {
  left: 2px;
  right: auto;
}
.rtl .infobox-small {
  text-align: right;
}
.rtl .infobox-small > .infobox-data {
  text-align: right;
}
.rtl .infobox-small > .infobox-chart > .sparkline {
  margin-left: auto;
  margin-right: 2px;
}
.rtl .infobox-small .percentage {
  margin-left: auto;
  margin-right: 2px;
}
.rtl .pricing-box .widget-header {
  padding-right: 0;
}
.rtl .pricing-table-header {
  text-align: right;
}
.rtl .pricing-table-header > li {
  padding: 7px 11px 7px 0;
}
.rtl .pricing-box-small {
  margin-left: 0;
  margin-right: -2px;
}
.rtl .pricing-span[class*="col-"] {
  float: right !important;
}
.rtl .pricing-span-header {
  float: right;
  padding-left: 0;
  padding-right: 12px;
}
@media only screen and (max-width: 768px) {
  .rtl .pricing-box:nth-child(odd) {
    padding-right: 12px !important;
    padding-left: 0 !important;
  }
  .rtl .pricing-box:nth-child(even) {
    padding-left: 12px !important;
    padding-right: 0 !important;
  }
}
.rtl .pricing-span {
  float: right !important;
}
@media only screen and (max-width: 460px) {
  .rtl .pricing-box,
  .rtl .pricing-box:nth-child(odd),
  .rtl .pricing-box:nth-child(even) {
    padding-left: 12px !important;
    padding-right: 12px !important;
  }
}
.rtl.login-layout .main-content {
  margin-right: 0;
}
.rtl.login-layout .login-box .toolbar > div:first-child {
  float: right;
  text-align: right;
}
.rtl.login-layout .login-box .toolbar > div:first-child > a {
  margin-left: 0;
  margin-right: 11px;
}
.rtl.login-layout .login-box .toolbar > div:first-child + div {
  float: left;
  text-align: left;
}
.rtl.login-layout .login-box .toolbar > div:first-child + div > a {
  margin-left: 11px;
  margin-right: 0;
}
.rtl .ace-thumbnails {
  margin-right: 0;
}
.rtl .ace-thumbnails > li {
  float: right;
}
.rtl .ace-thumbnails > li .tags {
  direction: ltr;
}
.rtl .ace-thumbnails > li .tags > .label-holder {
  margin: 1px 0 0 1px;
  direction: rtl;
  text-align: right;
}
.rtl .itemdiv {
  padding-right: 0;
  padding-left: 3px;
}
.rtl .itemdiv > .user {
  left: auto;
  right: 0;
}
.rtl .itemdiv > .body {
  margin-right: 50px;
  margin-left: 12px;
}
.rtl .itemdiv > .body > .time {
  right: auto;
  left: 9px;
}
.rtl .itemdiv > .body > .text {
  padding-left: 0;
  padding-right: 7px;
}
.rtl .itemdiv > .body > .text:after {
  right: 16px;
  left: -12px;
}
.rtl .itemdiv > .body > .text > [class*="icon-quote-"]:first-child {
  margin-left: 4px;
  margin-right: 0;
}
.rtl .itemdiv.dialogdiv:before {
  left: auto;
  right: 19px;
}
.rtl .itemdiv.dialogdiv > .body {
  border-left-width: 1px;
  border-right-width: 2px;
  margin-left: 1px;
}
.rtl .itemdiv.dialogdiv > .body:before {
  left: auto;
  right: -7px;
  border-width: 2px 2px 0 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
.rtl .itemdiv.dialogdiv > .body > .time {
  float: left;
}
.rtl .itemdiv.dialogdiv > .body > .text {
  padding-right: 0;
}
.rtl .itemdiv.memberdiv {
  float: right;
}
.rtl .itemdiv .tools {
  right: auto;
  left: 4px;
}
.rtl .itemdiv.commentdiv .tools {
  right: auto;
  left: 9px;
}
.rtl .item-list {
  margin: 0;
}
.rtl .item-list > li {
  border-left-width: 1px;
  border-right-width: 3px;
  border-left-color: #DDD;
}
.rtl li[class*="item-"] {
  border-left-width: 1px;
  border-right-width: 3px;
  border-left-color: #DDD;
}
.rtl li.item-orange {
  border-right-color: #e8b110;
}
.rtl li.item-orange2 {
  border-right-color: #f79263;
}
.rtl li.item-red {
  border-right-color: #d53f40;
}
.rtl li.item-red2 {
  border-right-color: #d15b47;
}
.rtl li.item-green {
  border-right-color: #9abc32;
}
.rtl li.item-green2 {
  border-right-color: #0490a6;
}
.rtl li.item-blue {
  border-right-color: #4f99c6;
}
.rtl li.item-blue2 {
  border-right-color: #3983c2;
}
.rtl li.item-blue3 {
  border-right-color: #1144eb;
}
.rtl li.item-pink {
  border-right-color: #cb6fd7;
}
.rtl li.item-black {
  border-right-color: #505050;
}
.rtl li.item-grey {
  border-right-color: #a0a0a0;
}
.rtl li.item-brown {
  border-right-color: #a52a2a;
}
.rtl li.item-default {
  border-right-color: #abbac3;
}
.rtl li.item-purple {
  border-right-color: #6f3cc4;
}
.rtl .profile-info-name {
  text-align: left;
  padding-right: 0;
  padding-left: 10px;
  left: auto;
  right: 0;
}
.rtl .profile-info-value {
  padding-right: 6px;
  padding-left: 4px;
  margin-left: auto;
  margin-right: 120px;
}
.rtl .profile-info-value > span + span:before {
  margin-left: 3px;
  margin-right: 1px;
}
.rtl .profile-user-info-striped .profile-info-value {
  padding-left: 0;
  padding-right: 12px;
}
.rtl .profile-activity img,
.rtl .profile-activity .thumbicon {
  margin-right: 0;
  margin-left: 10px;
}
.rtl .profile-activity .tools {
  left: 12px;
  right: auto;
}
.rtl .user-profile .user-title-label + .dropdown-menu {
  margin-left: auto;
  margin-right: -12px;
}
.rtl .user-status {
  margin-right: auto;
  margin-left: 1px;
}
.rtl .tab-content.profile-edit-tab-content {
  -webkit-box-shadow: -1px 1px 0 0 rgba(0, 0, 0, 0.2);
  box-shadow: -1px 1px 0 0 rgba(0, 0, 0, 0.2);
}
.rtl .inbox-tabs.nav-tabs > li.active > a.btn-new-mail > .btn:before {
  left: auto;
  right: 35%;
  right: calc(50% - 6px);
}
.rtl .inbox-tabs.nav-tabs.tab-size-bigger > li.active > a.btn-new-mail > .btn:before {
  left: auto;
  right: 35%;
  right: calc(50% - 8px);
}
.rtl .inbox-tabs.nav-tabs > li.pull-left {
  float: left;
}
@media only screen and (max-width: 475px) {
  .rtl .inbox-tabs > .li-new-mail {
    text-align: left;
  }
}
.rtl .message-item .sender {
  margin-left: 4px;
  margin-right: 6px;
}
.rtl .message-item .summary {
  margin-left: auto;
  margin-right: 30px;
}
.rtl .message-item .message-flags {
  right: auto;
  left: 101%;
  left: calc(100% + 4px);
}
.rtl .message-item .time {
  float: left;
}
.rtl .message-item .attachment {
  float: left;
}
.rtl .message-star {
  margin-left: 4px;
  margin-right: 6px;
}
.rtl .mail-tag:empty {
  margin-left: 1px;
  margin-right: 0;
}
.rtl ul.attachment-list {
  margin-left: 0;
  margin-right: 8px;
}
.rtl .attached-file > [class*="icon-"] {
  margin-right: auto;
  margin-left: 2px;
}
.rtl .messagebar-item-left,
.rtl .messagebar-item-right {
  text-align: right;
}
.rtl .message-navbar .nav-search {
  left: auto;
  right: 60px;
}
.rtl .inbox-folders .btn > [class*="icon-"]:first-child {
  text-align: right;
}
.rtl .inbox-folders .btn.active:before {
  left: auto;
  right: -1px;
  border-left: none;
  border-right: 3px solid #4F99C6;
}
.rtl .inbox-folders .btn .counter {
  right: auto;
  left: 8px;
}
.rtl .message-form .controls {
  margin-right: 125px;
}
.rtl .timeline-container:before {
  right: 28px;
  left: auto;
}
.rtl .timeline-item .transparent.widget-box {
  border-right: 3px solid #DAE1E5;
  border-left: none;
}
.rtl .timeline-item .transparent .widget-header > :first-child {
  margin-left: auto;
  margin-right: 8px;
}
.rtl .timeline-item:nth-child(even) .widget-box.transparent {
  border-right-color: #DBDBDB !important;
}
.rtl .timeline-item .widget-box {
  margin-left: auto;
  margin-right: 60px;
}
.rtl .timeline-info {
  float: right;
}
.rtl .timeline-label {
  margin-right: 34px;
  margin-left: auto;
}
.rtl .timeline-style2:before {
  display: none;
}
.rtl .timeline-style2 .timeline-item:before {
  left: auto;
  right: 90px;
}
.rtl .timeline-style2 .timeline-item .transparent.widget-box {
  border-right: none !important;
}
.rtl .timeline-style2 .timeline-indicator {
  left: auto;
  right: 86px;
}
.rtl .timeline-style2 .timeline-date {
  text-align: left;
  margin-right: auto;
  margin-left: 25px;
}
.rtl .timeline-style2 .timeline-item .widget-box {
  margin-left: auto;
  margin-right: 112px;
}
.rtl .timeline-style2 .timeline-label {
  margin-right: 0;
  text-align: left;
}
.rtl .ace-settings-container {
  left: 0;
  right: auto;
}
.rtl .btn.ace-settings-btn {
  float: right;
  border-radius: 0 6px 6px 0 !important;
}
.rtl .ace-settings-box {
  float: right;
}
.rtl .grid2,
.rtl .grid3,
.rtl .grid4 {
  float: right;
  border-left: none;
  border-right: 1px solid #E3E3E3;
}
.rtl .grid2:first-child,
.rtl .grid3:first-child,
.rtl .grid4:first-child {
  border-right: none;
}
.rtl .easyPieChart canvas {
  left: auto;
  right: 0;
}
.rtl .external-event > [class*="icon-"]:first-child {
  margin-right: 0;
  margin-left: 5px;
  border-right: none;
  border-left: 1px solid #FFF;
}
.rtl #cboxCurrent {
  left: auto;
  right: 64px;
}
.rtl #cboxNext,
.rtl #cboxPrevious {
  margin-left: 0;
  margin-right: 5px;
}
.rtl #cboxPrevious {
  left: auto;
  right: 27px;
}
.rtl #cboxNext {
  left: auto;
  right: 0;
}
.rtl .ace-spinner .spinner-buttons > button.btn:active {
  left: auto;
  top: auto;
}
.rtl .wizard-steps {
  margin-right: 0;
}
.rtl .wizard-actions {
  text-align: left;
}
.rtl .wizard-steps li:first-child:before {
  right: 50%;
  left: auto;
}
.rtl .tree {
  padding-left: 0;
  padding-right: 9px;
}
.rtl .tree:before {
  left: auto;
  right: 0;
  border-width: 0 1px 0 0;
}
.rtl .tree .tree-folder .tree-folder-header .tree-folder-name {
  margin-left: 0;
  margin-right: 2px;
}
.rtl .tree .tree-folder .tree-folder-header > [class*="icon-"]:first-child {
  margin: -2px -2px 0 0;
}
.rtl .tree .tree-folder:last-child:after {
  left: auto;
  right: -15px;
  border-left: none;
  border-right: 1px solid #FFF;
}
.rtl .tree .tree-folder .tree-folder-content {
  margin-left: 0;
  margin-right: 23px;
}
.rtl .tree .tree-folder .tree-folder-content:before {
  left: auto;
  right: -14px;
  border-width: 0 1px 0 0;
}
.rtl .tree .tree-item .tree-item-name {
  margin-left: 0;
  margin-right: 3px;
}
.rtl .tree .tree-item .tree-item-name > [class*="icon-"]:first-child {
  margin-right: 0;
  margin-left: 3px;
}
.rtl .tree .tree-folder:before,
.rtl .tree .tree-item:before {
  left: auto;
  right: -13px;
}
.rtl .tree .tree-loading {
  margin-left: 0;
  margin-right: 36px;
}
.rtl #gritter-notice-wrapper {
  text-align: right;
  direction: rtl;
  left: 20px;
  right: auto;
}
.rtl .gritter-close {
  right: auto;
  left: 3px;
}
.rtl .gritter-image {
  float: right;
}
.rtl .gritter-with-image,
.rtl .gritter-without-image {
  float: left;
}
.rtl .wysiwyg-toolbar .dropdown-menu {
  text-align: right;
}
.rtl .wysiwyg-toolbar .wysiwyg-choose-file {
  margin-left: auto;
}
.rtl .wysiwyg-toolbar .btn-group > .btn,
.rtl .wysiwyg-toolbar .btn-group > .inline > .btn {
  float: none;
}
.rtl .wysiwyg-style1 .btn-group:after,
.rtl .wysiwyg-style2 .btn-group:after {
  left: auto;
  border-left: none;
  right: -2px;
  border-right: 1px solid #E1E6EA;
}
.rtl .wysiwyg-toolbar .dropdown-menu input[type=text] {
  margin-left: 0;
  margin-right: 8px;
}
.rtl .wysiwyg-toolbar .dropdown-menu .btn {
  margin-right: 1px;
  margin-left: 8px;
}
.rtl .widget-body .md-header {
  margin-left: 0;
  margin-right: 9px;
}
.rtl .widget-body .md-header .btn-inverse {
  padding-right: 0;
  padding-left: 5px;
}
.rtl .select2-container .select2-choice {
  padding-left: 0;
  padding-right: 8px;
}
.rtl .select2-container.select2-allowclear .select2-choice .select2-chosen {
  margin-right: auto;
  margin-left: 42px;
}
.rtl .select2-container .select2-choice > .select2-chosen {
  margin-left: 26px;
  margin-right: auto;
}
.rtl .select2-container .select2-choice abbr {
  right: auto;
  left: 20px;
}
.rtl .select2-container .select2-choice .select2-arrow {
  right: auto;
  left: 0;
}
.rtl .select2-container .select2-choice .select2-arrow b:before {
  right: 5px;
  left: auto;
}
.rtl .select2-container-multi .select2-choices li {
  float: right;
}
.rtl .select2-container-multi .select2-choices .select2-search-choice {
  margin: 3px 5px 3px 0;
  padding: 3px 18px 3px 5px;
}
.rtl .select2-results {
  margin-right: 0;
}
.rtl .select2-drop {
  direction: rtl;
  text-align: right;
}
.rtl .select2-drop input {
  padding-right: 5px;
  padding-left: 20px;
}
.rtl .select2-drop .select2-results {
  padding-right: 4px;
  padding-left: 0;
}
.rtl .select2-search:after {
  right: -20px;
  left: auto;
}
.rtl .select2-search input.select2-active {
  background-position: 0%;
}
.rtl .editable-buttons {
  margin-left: auto;
  margin-right: 1px;
}
.rtl .editable-buttons .btn {
  margin: 0 0 0 1px;
}
.rtl .editable-input .ace-spinner {
  margin-right: auto;
  margin-left: 8px;
}
.rtl .editable-inline .editable-slider {
  margin-right: auto;
  margin-left: 4px;
}
.rtl .tags .tag {
  padding-left: 22px;
  padding-right: 9px;
  text-shadow: -1px 1px 1px rgba(0, 0, 0, 0.15);
}
.rtl .tags .tag .close {
  float: none;
  left: 0;
  right: auto;
}
.rtl .ui-datepicker .ui-datepicker-prev:before {
  content: "\f061";
}
.rtl .ui-datepicker .ui-datepicker-next:before {
  content: "\f060";
}
.rtl .ui-menu,
.rtl .ui-dialog,
.rtl .ui-jqdialog {
  direction: rtl;
  text-align: right;
}
.rtl .ui-menu .ui-menu-item a .ui-menu-icon {
  float: left;
}
.rtl .ui-menu .ui-menu-item a .ui-menu-icon:before {
  content: "\f104";
}
.rtl .ui-dialog .ui-dialog-titlebar-close,
.rtl .ui-jqdialog .ui-jqdialog-titlebar-close {
  left: 8px !important;
  right: auto !important;
}
.rtl .ui-tabs .ui-tabs-nav li {
  float: right;
  margin-right: 0;
  margin-left: 0.2em;
}
.rtl .ui-tabs .ui-tabs-nav li a {
  float: right;
}
.rtl .ui-tabs .ui-tabs-nav li.ui-state-default > a {
  margin-right: auto;
  margin-left: -1px;
}
.rtl .ui-accordion .ui-accordion-header {
  padding-right: 24px;
  padding-left: 8px;
}
.rtl .ui-accordion .ui-accordion-header .ui-accordion-header-icon {
  position: absolute;
  left: auto;
  right: 10px;
}
.rtl .ui-accordion .ui-accordion-header .ui-accordion-header-icon:before {
  content: "\f0d9";
}
.rtl .ui-accordion .ui-accordion-header.ui-state-active .ui-accordion-header-icon:before {
  content: "\f0d7";
}
.rtl .ui-jqgrid .ui-jqgrid-hdiv {
  border-width: 1px 1px 0 0;
}
.rtl .ui-jqgrid .ui-jqgrid-labels th {
  border-right: none !important;
  border-left: 1px solid #E1E1E1 !important;
  text-align: right !important;
}
.rtl .ui-jqgrid .ui-jqgrid-labels th:first-child {
  border-right: 1px solid #E1E1E1 !important;
}
.rtl .ui-jqgrid-labels th[id*="_cb"]:first-child {
  text-align: center !important;
}
.rtl .ui-jqgrid-sortable {
  padding-left: 0;
  padding-right: 4px;
}
.rtl .ui-jqdialog-content .searchFilter table {
  margin-left: auto;
  margin-right: 4px;
}
.rtl .ui-jqdialog-content .searchFilter .add-group,
.rtl .ui-jqdialog-content .searchFilter .add-rule,
.rtl .ui-jqdialog-content .searchFilter .delete-group {
  margin-left: auto !important;
  margin-right: 4px !important;
}
.rtl .ui-jqdialog-content .CaptionTD {
  text-align: left;
}
.rtl .ui-jqdialog .ui-widget-header .ui-jqdialog-title {
  text-align: right;
  padding-left: 0;
  padding-right: 12px;
  float: right !important;
}
.rtl .dd {
  text-align: right;
  direction: rtl;
}
.rtl .dd-list {
  text-align: right;
  direction: rtl;
  margin-right: 0;
}
.rtl .dd-list .dd-list {
  padding-right: 30px;
  padding-left: 0;
}
.rtl .dd2-handle + .dd2-content,
.rtl .dd2-handle + .dd2-content[class*="btn-"] {
  padding-left: 0;
  padding-right: 44px;
}
.rtl .dd-item > button {
  float: right;
  margin: 5px 5px 5px 1px;
  left: auto;
  right: 1px;
}
.rtl .dd2-item.dd-item > button {
  margin-left: 5px;
  margin-right: 34px;
}
.rtl .dd-dragel > li > .dd-handle {
  border-right: 2px solid #777;
  border-left-width: 0;
}
.rtl .dd-list > li[class*="item-"] {
  border-left-width: 0;
  border-right-width: 0;
}
.rtl .dd-list > li[class*="item-"] > .dd-handle {
  border-right: 2px solid;
  border-right-color: inherit;
  border-left-color: #DAE2EA;
  border-left-width: 1px;
}
.rtl .dd-list > li > .dd-handle .sticker {
  right: auto;
  left: 0;
}
.rtl .dd2-handle,
.rtl .dd-dragel > li > .dd2-handle {
  left: auto;
  right: 0;
  border-width: 1px 0 0 1px;
}
.rtl .limiterBox {
  direction: rtl;
  text-align: right;
}
.rtl ol.linenums {
  margin-right: 33px;
}
.rtl ol.linenums li {
  padding-left: 0;
  padding-right: 12px;
}
.rtl .prettyprint.linenums {
  -webkit-box-shadow: -40px 0 0 #FBFBFC inset, -41px 0 0 #ECECF0 inset;
  box-shadow: -40px 0 0 #FBFBFC inset, -41px 0 0 #ECECF0 inset;
}
/** Responsive RTL **/
@media only screen and (max-width: 767px) {
  .rtl .ace-nav > li:nth-last-child(4) > .dropdown-menu {
    right: auto;
    left: -80px;
  }
  .rtl .ace-nav > li:nth-last-child(4) > .dropdown-menu:before,
  .rtl .ace-nav > li:nth-last-child(4) > .dropdown-menu:after {
    right: auto;
    left: 100px;
  }
  .rtl .ace-nav > li:nth-last-child(3) > .dropdown-menu {
    right: auto;
    left: -40px;
  }
  .rtl .ace-nav > li:nth-last-child(3) > .dropdown-menu:before,
  .rtl .ace-nav > li:nth-last-child(3) > .dropdown-menu:after {
    right: auto;
    left: 60px;
  }
}
@media only screen and (max-width: 480px) {
  .rtl .ace-nav > li:nth-last-child(4) > .dropdown-menu {
    right: auto;
    left: -120px;
  }
  .rtl .ace-nav > li:nth-last-child(4) > .dropdown-menu:before,
  .rtl .ace-nav > li:nth-last-child(4) > .dropdown-menu:after {
    right: auto;
    left: 140px;
  }
  .rtl .ace-nav > li:nth-last-child(3) > .dropdown-menu {
    right: auto;
    left: -80px;
  }
  .rtl .ace-nav > li:nth-last-child(3) > .dropdown-menu:before,
  .rtl .ace-nav > li:nth-last-child(3) > .dropdown-menu:after {
    right: auto;
    left: 100px;
  }
  .rtl .ace-nav > li:nth-last-child(2) > .dropdown-menu {
    right: auto;
    left: -50px;
  }
  .rtl .ace-nav > li:nth-last-child(2) > .dropdown-menu:before,
  .rtl .ace-nav > li:nth-last-child(2) > .dropdown-menu:after {
    right: auto;
    left: 70px;
  }
}
@media only screen and (max-width: 460px) {
  .rtl .ace-nav > li:nth-last-child(4) > .dropdown-menu {
    left: auto;
    right: -5px;
  }
  .rtl .ace-nav > li:nth-last-child(4) > .dropdown-menu:before,
  .rtl .ace-nav > li:nth-last-child(4) > .dropdown-menu:after {
    left: auto;
    right: 25px;
  }
  .rtl .ace-nav > li:nth-last-child(3) > .dropdown-menu {
    left: auto;
    right: -60px;
  }
  .rtl .ace-nav > li:nth-last-child(3) > .dropdown-menu:before,
  .rtl .ace-nav > li:nth-last-child(3) > .dropdown-menu:after {
    left: auto;
    right: 80px;
  }
  .rtl .ace-nav > li:nth-last-child(2) > .dropdown-menu {
    left: auto;
    right: -110px;
  }
  .rtl .ace-nav > li:nth-last-child(2) > .dropdown-menu:before,
  .rtl .ace-nav > li:nth-last-child(2) > .dropdown-menu:after {
    left: auto;
    right: 130px;
  }
}
@media only screen and (max-width: 460px) {
  .rtl .ace-nav > li {
    text-align: right;
    float: none !important;
  }
  .rtl .ace-nav > li:first-child {
    border-left: none;
    border-right: 1px solid #DDD;
  }
  .rtl .ace-nav > li:last-child {
    border-left: 1px solid #DDD;
  }
}
@media (min-width: 422px) and (max-width: 480px), (max-width: 340px) {
  .rtl .ace-nav .nav-user-photo {
    margin-left: 0;
  }
  .rtl .user-info {
    margin-left: auto;
    right: auto;
    margin-right: 1px;
    left: 2px;
  }
}
@media only screen and (max-width: 767px) {
  .rtl .nav-search {
    right: auto;
    left: 5px;
  }
}
@media only screen and (max-width: 991px) {
  .rtl .navbar-brand {
    margin-right: 0;
  }
  .rtl .sidebar {
    left: auto;
    box-shadow: -2px 1px 2px 0 rgba(0, 0, 0, 0.2);
    border-left-width: 1px;
    border-right-width: 0;
  }
  .rtl .sidebar.display,
  .rtl .sidebar.menu-min.display {
    left: auto;
    right: 0;
  }
  .rtl .sidebar.menu-min {
    left: auto;
    right: -50px;
  }
  .rtl .main-content {
    margin-left: auto !important;
    margin-right: 0 !important;
  }
  .rtl .menu-toggler {
    left: auto;
    margin-right: auto;
    right: 0;
    margin-left: 2px;
    padding-left: 0;
    padding-right: 33px;
  }
  .rtl .menu-toggler:before {
    left: auto;
    right: 4px;
  }
  .rtl .menu-toggler:after {
    left: auto;
    right: 4px;
  }
  .rtl .menu-toggler > .menu-text {
    left: auto;
    right: 0;
  }
  .rtl .menu-toggler > .menu-text:after {
    left: auto;
    right: -8px;
  }
  .rtl .breadcrumb {
    margin-left: auto;
    margin-right: 90px;
  }
}
/* move the icons to the line below */
@media only screen and (max-width: 460px) {
  .rtl .navbar .navbar-brand {
    display: block;
    float: none;
  }
}
/* gallery */
@media only screen and (max-width: 480px) {
  .rtl .ace-thumbnails > li {
    float: none;
  }
}
@media only screen and (max-width: 320px) {
  .rtl .breadcrumb {
    margin-left: 0;
    margin-right: 36px;
  }
  .rtl .menu-toggler > .menu-text:after {
    left: auto;
    right: -13px;
  }
}
@media only screen and (max-width: 480px) {
  .rtl .fc-header td {
    text-align: right;
  }
}
/* custom grid */
@media only screen and (max-width: 360px) {
  .rtl .grid2,
  .rtl .grid3,
  .rtl .grid4 {
    border-right: none;
  }
  .rtl .grid2 > [class*="pull-"],
  .rtl .grid3 > [class*="pull-"],
  .rtl .grid4 > [class*="pull-"] {
    right: auto;
    left: 11px;
  }
}
@media only screen and (max-width: 767px) {
  .rtl .help-inline,
  .rtl .input-icon + .help-inline {
    padding-right: 0;
  }
}
@media only screen and (max-width: 480px) {
  .rtl .profile-info-value {
    margin-left: auto;
    margin-right: 90px;
  }
  .rtl .profile-user-info-striped .profile-info-name {
    padding-right: 10px;
    padding-left: 0;
    text-align: right;
  }
  .rtl .profile-user-info-striped .profile-info-value {
    margin-left: auto;
    margin-right: 10px;
  }
}
